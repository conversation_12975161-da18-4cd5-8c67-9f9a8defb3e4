import 'package:flutter/material.dart';
import 'lib/services/order_type_preference_service.dart';

/// Simple test script to verify order type preference service functionality
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 Testing Order Type Preference Service...\n');
  
  // Test 1: Clear any existing preferences
  print('1. Clearing existing preferences...');
  final clearResult = await OrderTypePreferenceService.clearCurrentOrderType();
  print('   Clear result: $clearResult');
  
  // Test 2: Check if order type exists (should be false)
  print('\n2. Checking if order type exists (should be false)...');
  final hasOrderType = await OrderTypePreferenceService.hasCurrentOrderType();
  print('   Has order type: $hasOrderType');
  
  // Test 3: Get current order type (should be null)
  print('\n3. Getting current order type (should be null)...');
  final currentOrderType = await OrderTypePreferenceService.getCurrentOrderType();
  print('   Current order type: $currentOrderType');
  
  // Test 4: Set order type by name - Dine In
  print('\n4. Setting order type by name: "dine in"...');
  final setDineInResult = await OrderTypePreferenceService.setOrderTypeByName('dine in');
  print('   Set dine in result: $setDineInResult');
  
  // Test 5: Get current order type after setting dine in
  print('\n5. Getting current order type after setting dine in...');
  final dineInOrderType = await OrderTypePreferenceService.getCurrentOrderType();
  print('   Current order type: $dineInOrderType');
  
  // Test 6: Set order type by name - Delivery
  print('\n6. Setting order type by name: "delivery"...');
  final setDeliveryResult = await OrderTypePreferenceService.setOrderTypeByName('delivery');
  print('   Set delivery result: $setDeliveryResult');
  
  // Test 7: Get current order type after setting delivery
  print('\n7. Getting current order type after setting delivery...');
  final deliveryOrderType = await OrderTypePreferenceService.getCurrentOrderType();
  print('   Current order type: $deliveryOrderType');
  
  // Test 8: Set order type by name - Takeaway
  print('\n8. Setting order type by name: "takeaway"...');
  final setTakeawayResult = await OrderTypePreferenceService.setOrderTypeByName('takeaway');
  print('   Set takeaway result: $setTakeawayResult');
  
  // Test 9: Get current order type after setting takeaway
  print('\n9. Getting current order type after setting takeaway...');
  final takeawayOrderType = await OrderTypePreferenceService.getCurrentOrderType();
  print('   Current order type: $takeawayOrderType');
  
  // Test 10: Check if order type exists (should be true)
  print('\n10. Checking if order type exists (should be true)...');
  final hasOrderTypeAfter = await OrderTypePreferenceService.hasCurrentOrderType();
  print('    Has order type: $hasOrderTypeAfter');
  
  // Test 11: Get individual values
  print('\n11. Getting individual values...');
  final orderTypeId = await OrderTypePreferenceService.getCurrentOrderTypeId();
  final orderTypeName = await OrderTypePreferenceService.getCurrentOrderTypeName();
  print('    Order type ID: $orderTypeId');
  print('    Order type name: $orderTypeName');
  
  print('\n✅ Order Type Preference Service test completed!');
  print('\n📝 Summary:');
  print('   - Service can clear preferences');
  print('   - Service can check if preferences exist');
  print('   - Service can set order types by name');
  print('   - Service can retrieve stored order type data');
  print('   - Service handles different order type variations');
}
