import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../blocs/pos/pos_bloc.dart';
import '../blocs/pos/pos_event.dart';
import '../blocs/pos/pos_state.dart';

class CategoryCard extends StatelessWidget {
  final Map<String, String> category;

  const CategoryCard({
    super.key,
    required this.category,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<POSBloc, POSState>(
      builder: (context, state) {
        final isSelected = category["name"] == state.selectedCategory;
        return GestureDetector(
          onTap: () =>
              context.read<POSBloc>().add(CategorySelected(category["name"]!)),
          child: Container(
            width: MediaQuery.of(context).orientation == Orientation.portrait
                ? 12.w
                : 8.w,
            padding: EdgeInsets.all(1.w),
            margin: EdgeInsets.symmetric(horizontal: 0.5.w),
            decoration: BoxDecoration(
              color:
                  isSelected ? Color.fromRGBO(44, 191, 90, 1) : Colors.white24,
              borderRadius: BorderRadius.circular(1.w),
              border: Border.all(
                color: Colors.grey.shade300,
                width: isSelected ? 0 : 1,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: Color.fromRGBO(0, 0, 0, 0.6),
                        blurRadius: 9,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : [],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category["icon"]!,
                  style: GoogleFonts.dmSans(
                    fontSize: 12.sp,
                  ),
                ),
                SizedBox(height: 1.h),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Text(
                    category["name"]!,
                    style: GoogleFonts.dmSans(
                      fontWeight: FontWeight.bold,
                      fontSize: 9.sp,
                      color: isSelected ? Colors.white : Colors.white,
                    ),
                  ),
                ),
                Text(
                  '${category["itemCount"]} items',
                  style: GoogleFonts.dmSans(
                    fontSize: 8.sp,
                    color: isSelected ? Colors.white : Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
