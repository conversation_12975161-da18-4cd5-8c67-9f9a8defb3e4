import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/tax_service.dart';
import '../utils/currency_formatter.dart';

/// Test widget to display tax information and verify the tax service is working
class TaxDisplayTest extends StatefulWidget {
  const TaxDisplayTest({Key? key}) : super(key: key);

  @override
  State<TaxDisplayTest> createState() => _TaxDisplayTestState();
}

class _TaxDisplayTestState extends State<TaxDisplayTest> {
  late TaxService _taxService;
  double _testAmount = 100.0;

  @override
  void initState() {
    super.initState();
    _taxService = TaxService();
    _taxService.addListener(_onTaxServiceChanged);
  }

  @override
  void dispose() {
    _taxService.removeListener(_onTaxServiceChanged);
    super.dispose();
  }

  void _onTaxServiceChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Text(
                'Tax Service Test',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                ),
              ),
              const Spacer(),
              if (_taxService.isLoading)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () => _taxService.fetchCommonTaxes(forceRefresh: true),
                tooltip: 'Refresh taxes',
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Test amount input
          Row(
            children: [
              Text(
                'Test Amount: ',
                style: GoogleFonts.poppins(fontSize: 14),
              ),
              Expanded(
                child: TextFormField(
                  initialValue: _testAmount.toString(),
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                  onChanged: (value) {
                    final amount = double.tryParse(value);
                    if (amount != null) {
                      setState(() {
                        _testAmount = amount;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Error display
          if (_taxService.error != null)
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error, color: Colors.red, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _taxService.error!,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.red[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          
          // Tax information
          if (_taxService.taxes.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'Available Taxes (${_taxService.taxes.length}):',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            ..._taxService.taxes.map((tax) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Row(
                children: [
                  Icon(
                    tax.enabled ? Icons.check_circle : Icons.cancel,
                    size: 16,
                    color: tax.enabled ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '${tax.taxCode}: ${tax.taxName} (${tax.taxValue}${tax.isPercentage ? '%' : ''})',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: tax.enabled ? Colors.black87 : Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
          
          // Tax calculation
          if (_taxService.enabledTaxes.isNotEmpty) ...[
            const Divider(),
            Text(
              'Tax Calculation for ${CurrencyFormatter.format(_testAmount)}:',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            ..._taxService.getTaxBreakdown(_testAmount).map((breakdown) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    breakdown.displayText,
                    style: GoogleFonts.poppins(fontSize: 12),
                  ),
                  Text(
                    breakdown.formattedAmount,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.orange[700],
                    ),
                  ),
                ],
              ),
            )),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Grand Total:',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    CurrencyFormatter.format(_taxService.calculateGrandTotal(_testAmount)),
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.green[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          if (_taxService.taxes.isEmpty && !_taxService.isLoading)
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  const Icon(Icons.warning, color: Colors.amber, size: 16),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'No taxes loaded. Click refresh to fetch from API.',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
