import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import '../models/reservation_response_model.dart';
import '../widgets/reservation_action_buttons.dart';

class CalendarReservationsView extends StatefulWidget {
  final List<ReservationItem> reservations;
  final DateTime selectedDate;
  final Function(DateTime) onDateSelected;
  final VoidCallback? onReservationUpdated;

  const CalendarReservationsView({
    super.key,
    required this.reservations,
    required this.selectedDate,
    required this.onDateSelected,
    this.onReservationUpdated,
  });

  @override
  State<CalendarReservationsView> createState() => _CalendarReservationsViewState();
}

class _CalendarReservationsViewState extends State<CalendarReservationsView> {
  late DateTime _focusedDay;
  CalendarFormat _calendarFormat = CalendarFormat.month;

  @override
  void initState() {
    super.initState();
    _focusedDay = widget.selectedDate;
  }

  List<ReservationItem> _getReservationsForDay(DateTime day) {
    return widget.reservations.where((reservation) {
      return DateUtils.isSameDay(reservation.reservationTime, day);
    }).toList();
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return Colors.green.shade400;
      case 'pending':
        return Colors.orange.shade400;
      case 'arrived':
        return Colors.blue.shade400;
      case 'cancelled':
        return Colors.red.shade400;
      case 'no_show':
        return Colors.grey.shade400;
      default:
        return Colors.orange.shade400;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return Icons.check_circle;
      case 'pending':
        return Icons.schedule;
      case 'arrived':
        return Icons.person;
      case 'cancelled':
        return Icons.cancel;
      case 'no_show':
        return Icons.person_off;
      default:
        return Icons.schedule;
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedDayReservations = _getReservationsForDay(widget.selectedDate);
    
    return Column(
      children: [
        // Calendar widget
        Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.08),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
            ),
          ),
          child: TableCalendar<ReservationItem>(
            firstDay: DateTime.utc(2020, 1, 1),
            lastDay: DateTime.utc(2030, 12, 31),
            focusedDay: _focusedDay,
            calendarFormat: _calendarFormat,
            selectedDayPredicate: (day) {
              return DateUtils.isSameDay(widget.selectedDate, day);
            },
            eventLoader: _getReservationsForDay,
            startingDayOfWeek: StartingDayOfWeek.monday,
            calendarStyle: CalendarStyle(
              outsideDaysVisible: false,
              weekendTextStyle: GoogleFonts.poppins(color: Colors.white70),
              holidayTextStyle: GoogleFonts.poppins(color: Colors.white70),
              defaultTextStyle: GoogleFonts.poppins(color: Colors.white),
              selectedTextStyle: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              todayTextStyle: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              selectedDecoration: BoxDecoration(
                color: Colors.blue.shade600,
                shape: BoxShape.circle,
              ),
              todayDecoration: BoxDecoration(
                color: Colors.blue.shade400.withValues(alpha: 0.6),
                shape: BoxShape.circle,
              ),
              markerDecoration: BoxDecoration(
                color: Colors.orange.shade400,
                shape: BoxShape.circle,
              ),
              markersMaxCount: 3,
            ),
            headerStyle: HeaderStyle(
              formatButtonVisible: true,
              titleCentered: true,
              formatButtonShowsNext: false,
              formatButtonDecoration: BoxDecoration(
                color: Colors.blue.shade600,
                borderRadius: BorderRadius.circular(8),
              ),
              formatButtonTextStyle: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 12,
              ),
              titleTextStyle: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              leftChevronIcon: const Icon(
                Icons.chevron_left,
                color: Colors.white,
              ),
              rightChevronIcon: const Icon(
                Icons.chevron_right,
                color: Colors.white,
              ),
            ),
            daysOfWeekStyle: DaysOfWeekStyle(
              weekdayStyle: GoogleFonts.poppins(
                color: Colors.white70,
                fontWeight: FontWeight.w500,
              ),
              weekendStyle: GoogleFonts.poppins(
                color: Colors.white70,
                fontWeight: FontWeight.w500,
              ),
            ),
            onDaySelected: (selectedDay, focusedDay) {
              setState(() {
                _focusedDay = focusedDay;
              });
              widget.onDateSelected(selectedDay);
            },
            onFormatChanged: (format) {
              setState(() {
                _calendarFormat = format;
              });
            },
            onPageChanged: (focusedDay) {
              setState(() {
                _focusedDay = focusedDay;
              });
            },
          ),
        ),
        
        // Selected day reservations
        Expanded(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Text(
                    'Reservations for ${DateFormat('EEEE, MMM dd, yyyy').format(widget.selectedDate)}',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Expanded(
                  child: selectedDayReservations.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.event_busy,
                                size: 64,
                                color: Colors.white.withValues(alpha: 0.5),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No reservations for this date',
                                style: GoogleFonts.poppins(
                                  color: Colors.white.withValues(alpha: 0.7),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        )
                      : _buildTimelineView(selectedDayReservations),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineView(List<ReservationItem> reservations) {
    // Sort reservations by time
    reservations.sort((a, b) => a.reservationTime.compareTo(b.reservationTime));
    
    return ListView.builder(
      padding: const EdgeInsets.only(bottom: 16),
      itemCount: reservations.length,
      itemBuilder: (context, index) {
        final reservation = reservations[index];
        return _buildTimelineReservationCard(reservation);
      },
    );
  }

  Widget _buildTimelineReservationCard(ReservationItem reservation) {
    final statusColor = _getStatusColor(reservation.status);
    final statusIcon = _getStatusIcon(reservation.status);
    final isToday = DateUtils.isSameDay(reservation.reservationTime, DateTime.now());
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isToday
              ? Colors.blue.shade600
              : Colors.white.withValues(alpha: 0.1),
          width: isToday ? 2 : 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Time indicator
            Container(
              width: 4,
              height: 60,
              decoration: BoxDecoration(
                color: statusColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 16),
            
            // Reservation details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        DateFormat('HH:mm').format(reservation.reservationTime),
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: statusColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: statusColor.withValues(alpha: 0.5)),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(statusIcon, color: statusColor, size: 12),
                            const SizedBox(width: 4),
                            Text(
                              reservation.status,
                              style: GoogleFonts.poppins(
                                color: statusColor,
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    reservation.customerName,
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        'Table ${reservation.table.name}',
                        style: GoogleFonts.poppins(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Icon(Icons.people, color: Colors.white70, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '${reservation.numberOfGuests} guests',
                        style: GoogleFonts.poppins(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  if (reservation.phoneNumber.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      reservation.phoneNumber,
                      style: GoogleFonts.poppins(
                        color: Colors.blue.shade400,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                  if (reservation.specialNotes != null && reservation.specialNotes!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Notes: ${reservation.specialNotes}',
                      style: GoogleFonts.poppins(
                        color: Colors.white60,
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
            
            // Action buttons
            ReservationActionButtons(
              reservationId: reservation.reservationId,
              status: reservation.status,
              onActionCompleted: widget.onReservationUpdated,
            ),
          ],
        ),
      ),
    );
  }
}
