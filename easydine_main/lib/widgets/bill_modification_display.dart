import 'package:easydine_main/utils/currency_formatter.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/bill_modification_service.dart';

/// Widget to display current bill modifications (tip and discount)
class BillModificationDisplay extends StatefulWidget {
  const BillModificationDisplay({Key? key}) : super(key: key);

  @override
  State<BillModificationDisplay> createState() => _BillModificationDisplayState();
}

class _BillModificationDisplayState extends State<BillModificationDisplay> {
  late BillModificationService _billModService;

  @override
  void initState() {
    super.initState();
    _billModService = BillModificationService();
    _billModService.addListener(_onBillModificationChanged);
  }

  @override
  void dispose() {
    _billModService.removeListener(_onBillModificationChanged);
    super.dispose();
  }

  void _onBillModificationChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_billModService.hasModifications) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Bill Modifications',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.blue[700],
            ),
          ),
          const SizedBox(height: 8),
          if (_billModService.tipAmount > 0)
            _buildModificationRow(
              'Tip Amount',
              '${CurrencyFormatter.getSymbol()}${_billModService.tipAmount.toStringAsFixed(2)}',
              Icons.tips_and_updates,
              Colors.green,
            ),
          if (_billModService.discountAmount > 0)
            _buildModificationRow(
              'Discount Percent',
              '${_billModService.discountAmount.toStringAsFixed(2)}%',
              Icons.discount,
              Colors.orange,
            ),
          if (_billModService.redeemCode.isNotEmpty)
            _buildModificationRow(
              'Redeem Code',
              _billModService.redeemCode,
              Icons.redeem,
              Colors.purple,
            ),
          if (_billModService.requiresManagerApproval)
            Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _billModService.isManagerApproved 
                    ? Colors.green.withOpacity(0.1)
                    : Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  Icon(
                    _billModService.isManagerApproved 
                        ? Icons.check_circle 
                        : Icons.warning,
                    size: 16,
                    color: _billModService.isManagerApproved 
                        ? Colors.green 
                        : Colors.amber[700],
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _billModService.isManagerApproved
                          ? 'Manager approved'
                          : 'Manager approval required',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: _billModService.isManagerApproved 
                            ? Colors.green[700] 
                            : Colors.amber[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildModificationRow(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.white,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
