import 'package:equatable/equatable.dart';

/// State class to manage bill modifications (tip and discount)
class BillModificationState extends Equatable {
  final double tipAmount;
  final double discountAmount;
  final String redeemCode;
  final bool isManagerApproved;
  final DateTime? lastModified;

  const BillModificationState({
    this.tipAmount = 0.0,
    this.discountAmount = 0.0,
    this.redeemCode = '',
    this.isManagerApproved = false,
    this.lastModified,
  });

  /// Check if there are any modifications
  bool get hasModifications => tipAmount > 0 || discountAmount > 0 || redeemCode.isNotEmpty;

  /// Check if discount requires manager approval
  bool get requiresManagerApproval => discountAmount > 0;

  /// Check if modifications are valid for submission
  bool get isValidForSubmission {
    if (requiresManagerApproval && !isManagerApproved) {
      return false;
    }
    return true;
  }

  /// Create a copy with updated values
  BillModificationState copyWith({
    double? tipAmount,
    double? discountAmount,
    String? redeemCode,
    bool? isManagerApproved,
    DateTime? lastModified,
  }) {
    return BillModificationState(
      tipAmount: tipAmount ?? this.tipAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      redeemCode: redeemCode ?? this.redeemCode,
      isManagerApproved: isManagerApproved ?? this.isManagerApproved,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  /// Reset to initial state
  BillModificationState reset() {
    return const BillModificationState();
  }

  /// Convert to JSON for API submission
  Map<String, dynamic> toJson() {
    return {
      if (tipAmount > 0) 'tipAmount': tipAmount,
      if (discountAmount > 0) 'discountAmount': discountAmount,
      if (redeemCode.isNotEmpty) 'redeemCode': redeemCode,
    };
  }

  @override
  List<Object?> get props => [
        tipAmount,
        discountAmount,
        redeemCode,
        isManagerApproved,
        lastModified,
      ];

  @override
  String toString() {
    return 'BillModificationState(tipAmount: $tipAmount, discountAmount: $discountAmount, redeemCode: $redeemCode, isManagerApproved: $isManagerApproved)';
  }
}
