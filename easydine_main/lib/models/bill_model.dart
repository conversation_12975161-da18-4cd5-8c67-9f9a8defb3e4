import 'package:equatable/equatable.dart';

/// Response model for table bill API
class TableBillResponse extends Equatable {
  final int statusCode;
  final bool success;
  final String message;
  final TableBillData data;

  const TableBillResponse({
    required this.statusCode,
    required this.success,
    required this.message,
    required this.data,
  });

  factory TableBillResponse.fromJson(Map<String, dynamic> json) {
    return TableBillResponse(
      statusCode: json['statusCode'] ?? 200,
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: TableBillData.fromJson(json['data'] ?? {}),
    );
  }

  @override
  List<Object?> get props => [statusCode, success, message, data];
}

/// Table bill data containing bills and summary
class TableBillData extends Equatable {
  final BillsByStatus bills;
  final BillSummary summary;

  const TableBillData({
    required this.bills,
    required this.summary,
  });

  factory TableBillData.fromJson(Map<String, dynamic> json) {
    return TableBillData(
      bills: BillsByStatus.fromJson(json['bills'] ?? {}),
      summary: BillSummary.fromJson(json['summary'] ?? {}),
    );
  }

  @override
  List<Object?> get props => [bills, summary];
}

/// Bills organized by status
class BillsByStatus extends Equatable {
  final List<TableBill> pending;
  final List<TableBill> paid;
  final List<TableBill> cancelled;

  const BillsByStatus({
    this.pending = const [],
    this.paid = const [],
    this.cancelled = const [],
  });

  factory BillsByStatus.fromJson(Map<String, dynamic> json) {
    return BillsByStatus(
      pending: _parseBillList(json['PENDING']),
      paid: _parseBillList(json['PAID']),
      cancelled: _parseBillList(json['CANCELLED']),
    );
  }

  static List<TableBill> _parseBillList(dynamic billsData) {
    if (billsData == null) return [];
    if (billsData is List) {
      return billsData.map((bill) => TableBill.fromJson(bill)).toList();
    }
    return [];
  }

  /// Get all bills regardless of status
  List<TableBill> get allBills => [...pending, ...paid, ...cancelled];

  @override
  List<Object?> get props => [pending, paid, cancelled];
}

/// Individual table bill model
class TableBill extends Equatable {
  final String billId;
  final String billNumber;
  final String orderDetailId;
  final String subtotal;
  final String tipAmount;
  final String totalTax;
  final String totalDiscount;
  final String serviceCharge;
  final String deliveryCharge;
  final String packagingCharge;
  final String totalAmount;
  final String status;
  final dynamic taxBreakdown;
  final dynamic discounts;
  final dynamic payments;
  final dynamic customerInfo;
  final String branchId;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final BillOrderDetails? orderDetails;

  const TableBill({
    required this.billId,
    required this.billNumber,
    required this.orderDetailId,
    required this.subtotal,
    required this.tipAmount,
    required this.totalTax,
    required this.totalDiscount,
    required this.serviceCharge,
    required this.deliveryCharge,
    required this.packagingCharge,
    required this.totalAmount,
    required this.status,
    this.taxBreakdown,
    this.discounts,
    this.payments,
    this.customerInfo,
    required this.branchId,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.orderDetails,
  });

  factory TableBill.fromJson(Map<String, dynamic> json) {
    return TableBill(
      billId: json['billId'] ?? '',
      billNumber: json['billNumber'] ?? '',
      orderDetailId: json['orderDetailId'] ?? '',
      subtotal: json['subtotal'] ?? '0.00',
      tipAmount: json['tipAmount'] ?? '0.00',
      totalTax: json['totalTax'] ?? '0.00',
      totalDiscount: json['totalDiscount'] ?? '0.00',
      serviceCharge: json['serviceCharge'] ?? '0.00',
      deliveryCharge: json['deliveryCharge'] ?? '0.00',
      packagingCharge: json['packagingCharge'] ?? '0.00',
      totalAmount: json['totalAmount'] ?? '0.00',
      status: json['status'] ?? '',
      taxBreakdown: json['taxBreakdown'],
      discounts: json['discounts'],
      payments: json['payments'],
      customerInfo: json['customerInfo'],
      branchId: json['branchId'] ?? '',
      notes: json['notes'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      orderDetails: json['orderDetails'] != null
          ? BillOrderDetails.fromJson(json['orderDetails'])
          : null,
    );
  }

  @override
  List<Object?> get props => [
        billId,
        billNumber,
        orderDetailId,
        subtotal,
        tipAmount,
        totalTax,
        totalDiscount,
        serviceCharge,
        deliveryCharge,
        packagingCharge,
        totalAmount,
        status,
        taxBreakdown,
        discounts,
        payments,
        customerInfo,
        branchId,
        notes,
        createdAt,
        updatedAt,
        orderDetails,
      ];
}

/// Order details within a bill
class BillOrderDetails extends Equatable {
  final String orderDetailId;
  final String orderCode;
  final BillTableInfo? table;
  final String total;
  final String status;

  const BillOrderDetails({
    required this.orderDetailId,
    required this.orderCode,
    this.table,
    required this.total,
    required this.status,
  });

  factory BillOrderDetails.fromJson(Map<String, dynamic> json) {
    return BillOrderDetails(
      orderDetailId: json['orderDetailId'] ?? '',
      orderCode: json['orderCode'] ?? '',
      table: json['table'] != null ? BillTableInfo.fromJson(json['table']) : null,
      total: json['total'] ?? '0.00',
      status: json['status'] ?? '',
    );
  }

  @override
  List<Object?> get props => [orderDetailId, orderCode, table, total, status];
}

/// Table information within bill order details
class BillTableInfo extends Equatable {
  final String id;
  final String name;

  const BillTableInfo({
    required this.id,
    required this.name,
  });

  factory BillTableInfo.fromJson(Map<String, dynamic> json) {
    return BillTableInfo(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
    );
  }

  @override
  List<Object?> get props => [id, name];
}

/// Bill summary information
class BillSummary extends Equatable {
  final int totalBills;
  final double totalAmount;
  final double pendingAmount;

  const BillSummary({
    required this.totalBills,
    required this.totalAmount,
    required this.pendingAmount,
  });

  factory BillSummary.fromJson(Map<String, dynamic> json) {
    return BillSummary(
      totalBills: json['totalBills'] ?? 0,
      totalAmount: (json['totalAmount'] ?? 0).toDouble(),
      pendingAmount: (json['pendingAmount'] ?? 0).toDouble(),
    );
  }

  @override
  List<Object?> get props => [totalBills, totalAmount, pendingAmount];
}
