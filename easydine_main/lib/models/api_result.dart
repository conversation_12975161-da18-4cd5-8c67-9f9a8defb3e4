/// Generic result class for API operations
class ApiResult<T> {
  final bool success;
  final T? data;
  final String? errorMessage;
  final int? statusCode;

  const ApiResult({
    required this.success,
    this.data,
    this.errorMessage,
    this.statusCode,
  });

  /// Create a successful result
  factory ApiResult.success(T data) {
    return ApiResult(
      success: true,
      data: data,
    );
  }

  /// Create a successful result without data
  factory ApiResult.successNoData() {
    return ApiResult(
      success: true,
    );
  }

  /// Create a failed result with error message
  factory ApiResult.failure(String errorMessage, {int? statusCode}) {
    return ApiResult(
      success: false,
      errorMessage: errorMessage,
      statusCode: statusCode,
    );
  }

  /// Create a failed result from exception
  factory ApiResult.exception(Exception exception) {
    return ApiResult(
      success: false,
      errorMessage: exception.toString(),
    );
  }
}
