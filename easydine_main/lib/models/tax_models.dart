import 'package:equatable/equatable.dart';

/// Tax model representing a single tax configuration
class Tax extends Equatable {
  final String taxId;
  final String branchId;
  final String taxCode;
  final String taxName;
  final String description;
  final String taxValue;
  final String taxType;
  final String category;
  final bool enabled;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;

  const Tax({
    required this.taxId,
    required this.branchId,
    required this.taxCode,
    required this.taxName,
    required this.description,
    required this.taxValue,
    required this.taxType,
    required this.category,
    required this.enabled,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  /// Get tax value as double
  double get taxValueAsDouble => double.tryParse(taxValue) ?? 0.0;

  /// Check if tax is percentage based
  bool get isPercentage => taxType.toLowerCase() == 'percent';

  /// Check if tax is fixed amount
  bool get isFixedAmount => taxType.toLowerCase() == 'fixed';

  /// Calculate tax amount for a given subtotal
  double calculateTaxAmount(double subtotal) {
    if (!enabled) return 0.0;
    
    if (isPercentage) {
      return subtotal * (taxValueAsDouble / 100);
    } else if (isFixedAmount) {
      return taxValueAsDouble;
    }
    
    return 0.0;
  }

  /// Get display text for tax (e.g., "VAT (20%)" or "Service Fee (₹50)")
  String get displayText {
    if (isPercentage) {
      return '$taxCode (${taxValueAsDouble.toStringAsFixed(1)}%)';
    } else if (isFixedAmount) {
      return '$taxCode (₹${taxValueAsDouble.toStringAsFixed(2)})';
    }
    return taxCode;
  }

  factory Tax.fromJson(Map<String, dynamic> json) {
    return Tax(
      taxId: json['taxId'] ?? '',
      branchId: json['branchId'] ?? '',
      taxCode: json['taxCode'] ?? '',
      taxName: json['taxName'] ?? '',
      description: json['description'] ?? '',
      taxValue: json['taxValue'] ?? '0',
      taxType: json['taxType'] ?? 'percent',
      category: json['category'] ?? 'common',
      enabled: json['enabled'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      deletedAt: json['deletedAt'] != null ? DateTime.parse(json['deletedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'taxId': taxId,
      'branchId': branchId,
      'taxCode': taxCode,
      'taxName': taxName,
      'description': description,
      'taxValue': taxValue,
      'taxType': taxType,
      'category': category,
      'enabled': enabled,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        taxId,
        branchId,
        taxCode,
        taxName,
        description,
        taxValue,
        taxType,
        category,
        enabled,
        createdAt,
        updatedAt,
        deletedAt,
      ];
}

/// API response model for tax data
class TaxApiResponse extends Equatable {
  final int statusCode;
  final bool success;
  final String message;
  final TaxData data;

  const TaxApiResponse({
    required this.statusCode,
    required this.success,
    required this.message,
    required this.data,
  });

  factory TaxApiResponse.fromJson(Map<String, dynamic> json) {
    return TaxApiResponse(
      statusCode: json['statusCode'] ?? 200,
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: TaxData.fromJson(json['data'] ?? {}),
    );
  }

  @override
  List<Object> get props => [statusCode, success, message, data];
}

/// Tax data container
class TaxData extends Equatable {
  final List<Tax> taxes;
  final int totalCount;

  const TaxData({
    required this.taxes,
    required this.totalCount,
  });

  /// Get enabled taxes only
  List<Tax> get enabledTaxes => taxes.where((tax) => tax.enabled).toList();

  /// Calculate total tax amount for a given subtotal
  double calculateTotalTaxAmount(double subtotal) {
    return enabledTaxes.fold(0.0, (total, tax) => total + tax.calculateTaxAmount(subtotal));
  }

  factory TaxData.fromJson(Map<String, dynamic> json) {
    final dataList = json['data'] as List<dynamic>? ?? [];
    final taxes = dataList.map((taxJson) => Tax.fromJson(taxJson)).toList();
    
    return TaxData(
      taxes: taxes,
      totalCount: json['totalCount'] ?? taxes.length,
    );
  }

  @override
  List<Object> get props => [taxes, totalCount];
}
