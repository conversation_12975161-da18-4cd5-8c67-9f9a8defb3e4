import 'package:easydine_main/widgets/app_bar.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../../widgets/tiled_background.dart';
import '../../services/order_service.dart';
import '../../services/thermal_print_service.dart';
import '../../models/order_model.dart';

class OrderHistoryPage extends StatefulWidget {
  const OrderHistoryPage({super.key});

  @override
  State<OrderHistoryPage> createState() => _OrderHistoryPageState();
}

class _OrderHistoryPageState extends State<OrderHistoryPage> {
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  List<OrderDetail> orders = [];
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadOrderHistory();
  }

  Future<void> _loadOrderHistory() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      // Use the new endpoint specifically for completed orders
      final response = await OrderService.getCompletedOrders(limit: 50);

      if (response != null && response.success) {
        // The new endpoint should already return only completed/cancelled orders
        // but we can still filter as a safety measure
        final filteredOrders = response.data.data.where((order) {
          final status = order.status.toUpperCase();
          return status == 'COMPLETED' || status == 'CANCELLED' || status == 'SERVED';
        }).toList();

        setState(() {
          orders = filteredOrders;
          isLoading = false;
        });
      } else {
        setState(() {
          orders = [];
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load completed orders: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      key: scaffoldKey,
      appBar: WaiterAppBar(scaffoldKey: scaffoldKey),
      body: Stack(
        children: [
          TiledBackground(),
          SafeArea(
            child: Column(
              children: [
                _buildPageHeader(),
                Expanded(child: _buildContent()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleRefundOrder(OrderDetail order, BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.money_off, color: Colors.orange),
            SizedBox(width: 8),
            Text(
              'Refund Order',
              style: GoogleFonts.dmSans(color: Colors.white),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to process a refund for this order?',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Order: ${order.orderCode}',
                    style: GoogleFonts.dmSans(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Table: ${order.table?.name ?? 'Unknown'}',
                    style: GoogleFonts.dmSans(color: Colors.white70),
                  ),
                  Text(
                    'Amount: \$${order.total}',
                    style: GoogleFonts.dmSans(
                      color: Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processRefund(order, context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
            ),
            child: Text(
              'Process Refund',
              style: GoogleFonts.dmSans(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _processRefund(OrderDetail order, BuildContext context) {
    // TODO: Implement actual refund processing with backend API
    // For now, show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Refund processed for order ${order.orderCode}',
          style: GoogleFonts.dmSans(),
        ),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _handlePrintBill(OrderDetail order, BuildContext context) async {
    try {
      debugPrint('🖨️ Printing bill for order ${order.orderCode}');

      // Get the first bill from the order
      final bill = order.bills.isNotEmpty ? order.bills.first : null;
      if (bill == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'No bill data available for this order',
              style: GoogleFonts.dmSans(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Create thermal bill data
      final thermalBillData = ThermalBillData.fromOrderAndBill(order, bill);

      // Initialize thermal print service
      final thermalPrintService = ThermalPrintService();

      // Print the bill
      await thermalPrintService.printBill(thermalBillData);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Bill sent to printer',
            style: GoogleFonts.dmSans(),
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    } catch (e) {
      debugPrint('❌ Error printing bill: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Error printing bill: ${e.toString()}',
            style: GoogleFonts.dmSans(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleShareBill(OrderDetail order, BuildContext context) async {
    try {
      debugPrint('📤 Sharing bill for order ${order.orderCode}');

      // Get the first bill from the order
      final bill = order.bills.isNotEmpty ? order.bills.first : null;
      if (bill == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'No bill data available for this order',
              style: GoogleFonts.dmSans(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Create thermal bill data
      final thermalBillData = ThermalBillData.fromOrderAndBill(order, bill);

      // Initialize thermal print service
      final thermalPrintService = ThermalPrintService();

      // Share the bill
      await thermalPrintService.shareAndSaveBill(thermalBillData);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Bill shared successfully',
            style: GoogleFonts.dmSans(),
          ),
          backgroundColor: Colors.blue,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    } catch (e) {
      debugPrint('❌ Error sharing bill: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Error sharing bill: ${e.toString()}',
            style: GoogleFonts.dmSans(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildPageHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade600.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade600),
            ),
            child: Icon(
              Icons.history,
              color: Colors.blue.shade400,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Completed Orders',
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'View completed and cancelled orders',
                  style: GoogleFonts.dmSans(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _loadOrderHistory,
            icon: Icon(
              Icons.refresh,
              color: Colors.blue.shade400,
            ),
            tooltip: 'Refresh',
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              'Loading completed orders...',
              style: GoogleFonts.dmSans(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            SizedBox(height: 16),
            Text(
              errorMessage!,
              style: GoogleFonts.dmSans(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadOrderHistory,
              child: Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              color: Colors.grey,
              size: 64,
            ),
            SizedBox(height: 16),
            Text(
              'No completed orders found',
              style: GoogleFonts.dmSans(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Completed and cancelled orders will appear here',
              style: GoogleFonts.dmSans(
                color: Colors.grey[400],
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadOrderHistory,
      child: _buildOrdersList(orders),
    );
  }

  Widget _buildOrdersList(List<OrderDetail> orders) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isLandscape =
            MediaQuery.of(context).orientation == Orientation.landscape;
        final isTablet = constraints.maxWidth > 600;

        if (isLandscape && isTablet) {
          // Grid layout for landscape tablets
          return MasonryGridView.count(
            padding: EdgeInsets.all(16),
            crossAxisCount: 3,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            itemCount: orders.length,
            itemBuilder: (context, index) {
              return _buildCompactOrderCard(orders[index]);
            },
          );
        } else {
          // List layout for portrait or phone
          return ListView.builder(
            padding: EdgeInsets.all(16),
            itemCount: orders.length,
            itemBuilder: (context, index) {
              final order = orders[index];
              return _buildOrderCard(order);
            },
          );
        }
      },
    );
  }

  Widget _buildCompactOrderCard(OrderDetail order) {
    final orderId = order.orderCode;
    final status = order.status;
    final total = order.total;
    final createdAt = order.createdAt;

    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.08),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  'Order #$orderId',
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              _buildStatusChip(status),
            ],
          ),
          SizedBox(height: 8),
          Text(
            'Total: \$${double.tryParse(total) != null ? double.parse(total).toStringAsFixed(2) : total}',
            style: GoogleFonts.dmSans(
              color: Color(0xFF2CBF5A),
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4),
          Text(
            _formatDate(createdAt),
            style: GoogleFonts.dmSans(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
          if (order.orderItems.isNotEmpty) ...[
            SizedBox(height: 8),
            Text(
              '${order.orderItems.length} item${order.orderItems.length > 1 ? 's' : ''}',
              style: GoogleFonts.dmSans(
                color: Colors.white60,
                fontSize: 11,
              ),
            ),
          ],
          SizedBox(height: 12),
          _buildCompactActionButtons(order),
        ],
      ),
    );
  }

  Widget _buildCompactActionButtons(OrderDetail order) {
    return Column(
      children: [
        Row(
          children: [
            // Print button
            Expanded(
              child: ElevatedButton(
                onPressed: () => _handlePrintBill(order, context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 6),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                child: Icon(Icons.print, size: 14),
              ),
            ),
            SizedBox(width: 6),
            // Share button
            Expanded(
              child: ElevatedButton(
                onPressed: () => _handleShareBill(order, context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 6),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                child: Icon(Icons.share, size: 14),
              ),
            ),
            SizedBox(width: 6),
            // Refund button
            Expanded(
              child: ElevatedButton(
                onPressed: () => _handleRefundOrder(order, context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 6),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                child: Icon(Icons.money_off, size: 14),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrderCard(OrderDetail order) {
    final orderId = order.orderCode;
    final status = order.status;
    final total = order.total;
    final createdAt =
        order.createdAt.toString().split(' ')[0]; // Just the date part
    final table = order.table?.name ?? 'Unknown';

    return Container(
      margin: EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.08),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Order #$orderId',
                style: GoogleFonts.dmSans(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getStatusColor(status),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  status,
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.table_restaurant, color: Colors.grey[400], size: 16),
              SizedBox(width: 4),
              Text(
                'Table $table',
                style: GoogleFonts.dmSans(
                  color: Colors.grey[400],
                  fontSize: 14,
                ),
              ),
              SizedBox(width: 16),
              Icon(Icons.access_time, color: Colors.grey[400], size: 16),
              SizedBox(width: 4),
              Text(
                createdAt,
                style: GoogleFonts.dmSans(
                  color: Colors.grey[400],
                  fontSize: 14,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total: \$$total',
                style: GoogleFonts.dmSans(
                  color: Colors.green,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          _buildActionButtons(order),
        ],
      ),
    );
  }

  Widget _buildActionButtons(OrderDetail order) {
    return Row(
      children: [
        // Print Bill Again button
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _handlePrintBill(order, context),
            icon: Icon(Icons.print, size: 16),
            label: Text(
              'Print Again',
              style: GoogleFonts.dmSans(fontSize: 12),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        SizedBox(width: 8),
        // Share Bill button
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _handleShareBill(order, context),
            icon: Icon(Icons.share, size: 16),
            label: Text(
              'Share Bill',
              style: GoogleFonts.dmSans(fontSize: 12),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        SizedBox(width: 8),
        // Refund button
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _handleRefundOrder(order, context),
            icon: Icon(Icons.money_off, size: 16),
            label: Text(
              'Refund',
              style: GoogleFonts.dmSans(fontSize: 12),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(String status) {
    final color = _getStatusColor(status);
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        status,
        style: GoogleFonts.dmSans(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return 'Today ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Yesterday ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays < 7) {
      final weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      return '${weekdays[dateTime.weekday - 1]} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return Colors.orange;
      case 'IN_PREPARATION':
        return Colors.blue;
      case 'READY':
        return Colors.green;
      case 'SERVED':
        return Colors.purple;
      case 'CANCELLED':
        return Colors.red;
      case 'CHECKOUT':
        return Colors.teal;
      case 'COMPLETED':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }
}
