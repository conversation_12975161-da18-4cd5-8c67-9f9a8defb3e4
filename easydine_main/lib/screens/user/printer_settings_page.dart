// lib/screens/user/printer_settings_page.dart

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';
import '../../blocs/printer_settings/printer_settings_bloc.dart';
import '../../models/printer_settings.dart';

class PrinterSettingsPage extends StatelessWidget {
  const PrinterSettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PrinterSettingsBloc()..add(LoadPrinterSettings()),
      child: const PrinterSettingsView(),
    );
  }
}

class PrinterSettingsView extends StatefulWidget {
  const PrinterSettingsView({Key? key}) : super(key: key);

  @override
  State<PrinterSettingsView> createState() => _PrinterSettingsViewState();
}

class _PrinterSettingsViewState extends State<PrinterSettingsView> {
  @override
  void initState() {
    super.initState();
    context.read<PrinterSettingsBloc>().add(LoadPrinterSettings());
    context.read<PrinterSettingsBloc>().add(LoadCustomLocations());
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: const Color(0xFF0F172A),
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.print_outlined,
                color: Colors.white,
                size: 28,
              ),
            ),
            const SizedBox(width: 20),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Printer Management',
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w700,
                    fontSize: isTablet ? 28 : 22,
                    color: Colors.white,
                    letterSpacing: -0.5,
                  ),
                ),
                Text(
                  'Configure and assign printers to stations',
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w400,
                    fontSize: isTablet ? 14 : 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ],
        ),
        backgroundColor: const Color(0xFF1E293B),
        foregroundColor: Colors.white,
        elevation: 0,
        toolbarHeight: isTablet ? 100 : 70,
        actions: [
          BlocBuilder<PrinterSettingsBloc, PrinterSettingsState>(
            builder: (context, state) {
              return Container(
                margin: const EdgeInsets.only(right: 8),
                child: ElevatedButton.icon(
                  onPressed: state.isDiscovering
                      ? null
                      : () {
                          context
                              .read<PrinterSettingsBloc>()
                              .add(DiscoverPrinters());
                        },
                  icon: state.isDiscovering
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2.5,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.radar, size: 20),
                  label: Text(
                    state.isDiscovering ? 'Scanning...' : 'Discover Printers',
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6366F1),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              );
            },
          ),
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: IconButton(
              icon: const Icon(Icons.refresh_rounded, size: 24),
              onPressed: () {
                context.read<PrinterSettingsBloc>().add(LoadPrinterSettings());
              },
              tooltip: 'Refresh',
              style: IconButton.styleFrom(
                backgroundColor: const Color(0xFF374151),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
      body: BlocConsumer<PrinterSettingsBloc, PrinterSettingsState>(
        listener: (context, state) {
          if (state.error != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.error!),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state.isLoading) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Color(0xFF6366F1)),
                    strokeWidth: 3,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Loading printer settings...',
                    style: GoogleFonts.inter(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            );
          }

          if (isTablet && isLandscape) {
            return _buildTabletLandscapeLayout(context, state);
          } else {
            return _buildMobileLayout(context, state);
          }
        },
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context, PrinterSettingsState state) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Customized Printing'),
          SizedBox(height: 3.h),
          _buildAssignPrintersSection(context, state),
          SizedBox(height: 4.h),
          _buildAssignCategoriesSection(context, state),
          SizedBox(height: 4.h),
          _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildTabletLandscapeLayout(
      BuildContext context, PrinterSettingsState state) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left Panel - Printer Discovery and Assignment
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFF1E293B),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: const Color(0xFF334155), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildModernSectionHeader(
                      'Available Printers', Icons.print_outlined,
                      subtitle: 'Add and manage your printers'),
                  const SizedBox(height: 24),
                  Expanded(
                    child: _buildModernPrinterGrid(context, state),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 24),
          // Right Panel - Category Assignment
          Expanded(
            flex: 2,
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFF1E293B),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: const Color(0xFF334155), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildModernSectionHeader(
                      'Category Assignment', Icons.category_outlined,
                      subtitle: 'Assign menu categories to printer locations'),
                  const SizedBox(height: 24),
                  Expanded(
                    child: _buildModernCategoriesGrid(context, state),
                  ),
                  const SizedBox(height: 24),
                  _buildModernActionButtons(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 6.sp,
        fontWeight: FontWeight.w700,
        color: Colors.white,
      ),
    );
  }

  Widget _buildAssignPrintersSection(
      BuildContext context, PrinterSettingsState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Assign Printers',
              style: GoogleFonts.poppins(
                fontSize: 4.5.sp,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            Row(
              children: [
                IconButton(
                  onPressed: () => _showAddPrinterDialog(context),
                  icon: const Icon(Icons.add, color: Colors.white),
                  tooltip: 'Add Printer',
                ),
                IconButton(
                  onPressed: () => _showAddLocationDialog(context),
                  icon: const Icon(Icons.add_location, color: Colors.white),
                  tooltip: 'Add Custom Location',
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 2.h),
        _buildPrinterGrid(context, state),
      ],
    );
  }

  Widget _buildPrinterGrid(BuildContext context, PrinterSettingsState state) {
    final printers = state.settings.availablePrinters;

    if (printers.isEmpty) {
      return Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[600]!),
        ),
        child: Column(
          children: [
            Icon(
              Icons.print_disabled,
              size: 12.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 2.h),
            Text(
              'No printers found',
              style: GoogleFonts.poppins(
                fontSize: 4.sp,
                color: Colors.grey[400],
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Tap the search icon to discover printers',
              style: GoogleFonts.poppins(
                fontSize: 3.sp,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: printers.length,
      itemBuilder: (context, index) {
        final printer = printers[index];
        return _buildPrinterCard(context, state, printer, index);
      },
    );
  }

  Widget _buildPrinterCard(BuildContext context, PrinterSettingsState state,
      PrinterDevice printer, int index) {
    final defaultLocations = PrinterLocation.getDefaultLocations();
    final allLocations = [...defaultLocations, ...state.customLocations];
    final savedLocation = state.settings.printerAssignments[printer.id];

    // Validate that the saved location still exists in available locations
    final currentLocation = (savedLocation != null &&
            (allLocations.contains(savedLocation) ||
                savedLocation == 'Not assigned'))
        ? savedLocation
        : 'Not assigned';

    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: printer.isConnected ? Colors.green : Colors.grey[600]!,
          width: printer.isConnected ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getPrinterIcon(printer.type),
                color: printer.isConnected ? Colors.green : Colors.grey[400],
                size: 6.w,
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      printer.name,
                      style: GoogleFonts.poppins(
                        fontSize: 4.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      '${printer.type.toUpperCase()} • ${printer.isConnected ? 'Connected' : 'Disconnected'}',
                      style: GoogleFonts.poppins(
                        fontSize: 3.sp,
                        color: printer.isConnected
                            ? Colors.green
                            : Colors.grey[400],
                      ),
                    ),
                  ],
                ),
              ),
              _buildConnectionButton(context, printer),
            ],
          ),
          if (printer.ipAddress != null || printer.macAddress != null) ...[
            SizedBox(height: 2.h),
            Text(
              _getPrinterDetails(printer),
              style: GoogleFonts.poppins(
                fontSize: 2.5.sp,
                color: Colors.grey[400],
              ),
            ),
          ],
          SizedBox(height: 2.h),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Assign to:',
                  style: GoogleFonts.poppins(
                    fontSize: 3.5.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                  decoration: BoxDecoration(
                    color: Colors.grey[700],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[600]!),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: currentLocation,
                      dropdownColor: Colors.grey[800],
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 3.sp,
                      ),
                      icon: const Icon(Icons.arrow_drop_down,
                          color: Colors.white),
                      items: ['Not assigned', ...allLocations]
                          .map((location) => DropdownMenuItem<String>(
                                value: location,
                                child: Text(location),
                              ))
                          .toList(),
                      onChanged: (value) {
                        if (value != null) {
                          context.read<PrinterSettingsBloc>().add(
                                UpdatePrinterAssignment(
                                  printerId: printer.id,
                                  location: value,
                                ),
                              );
                        }
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: printer.isConnected
                      ? () {
                          context.read<PrinterSettingsBloc>().add(
                                TestPrinter(printerId: printer.id),
                              );
                        }
                      : null,
                  icon: Icon(Icons.print, size: 4.w),
                  label: Text(
                    'Test Print',
                    style: GoogleFonts.poppins(fontSize: 3.sp),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 1.5.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    context.read<PrinterSettingsBloc>().add(
                          RemovePrinterDevice(printerId: printer.id),
                        );
                  },
                  icon: Icon(Icons.delete, size: 4.w),
                  label: Text(
                    'Remove',
                    style: GoogleFonts.poppins(fontSize: 3.sp),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 1.5.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildConnectionButton(BuildContext context, PrinterDevice printer) {
    return ElevatedButton(
      onPressed: () {
        if (printer.isConnected) {
          context.read<PrinterSettingsBloc>().add(
                DisconnectPrinter(printerId: printer.id),
              );
        } else {
          context.read<PrinterSettingsBloc>().add(
                ConnectToPrinter(printerId: printer.id),
              );
        }
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: printer.isConnected ? Colors.red : Colors.green,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text(
        printer.isConnected ? 'Disconnect' : 'Connect',
        style: GoogleFonts.poppins(fontSize: 2.5.sp),
      ),
    );
  }

  IconData _getPrinterIcon(String type) {
    switch (type.toLowerCase()) {
      case 'usb':
        return Icons.usb;
      case 'bluetooth':
        return Icons.bluetooth;
      case 'network':
        return Icons.wifi;
      case 'thermal':
        return Icons.receipt;
      default:
        return Icons.print;
    }
  }

  String _getPrinterDetails(PrinterDevice printer) {
    if (printer.ipAddress != null) {
      return 'IP: ${printer.ipAddress}:${printer.port ?? 9100}';
    } else if (printer.macAddress != null) {
      return 'MAC: ${printer.macAddress}';
    } else if (printer.deviceId != null) {
      return 'Device ID: ${printer.deviceId}';
    }
    return '';
  }

  Widget _buildAssignCategoriesSection(
      BuildContext context, PrinterSettingsState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Assign Categories',
          style: GoogleFonts.poppins(
            fontSize: 4.5.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 2.h),
        _buildCategoriesGrid(context, state),
      ],
    );
  }

  Widget _buildCategoriesGrid(
      BuildContext context, PrinterSettingsState state) {
    final assignedLocations = state.settings.getAssignedLocations();
    if (assignedLocations.isEmpty) {
      return Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[600]!),
        ),
        child: Text(
          'No printers assigned to locations yet. Please assign printers above first.',
          style: GoogleFonts.poppins(
            color: Colors.white70,
            fontSize: 3.5.sp,
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[600]!),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: assignedLocations.map((location) {
          return Expanded(
            child: _buildLocationColumn(context, state, location),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildLocationColumn(
      BuildContext context, PrinterSettingsState state, String location) {
    final categoriesForLocation =
        state.settings.getCategoriesForLocation(location);
    final availableCategories = state.availableCategories.isNotEmpty
        ? state.availableCategories
        : FoodCategory.getAllCategories();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          location,
          style: GoogleFonts.poppins(
            fontSize: 4.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 2.h),
        ...availableCategories.map((category) {
          final isAssigned = categoriesForLocation.contains(category);
          return Padding(
            padding: EdgeInsets.only(bottom: 1.h),
            child: Row(
              children: [
                Checkbox(
                  value: isAssigned,
                  onChanged: (value) {
                    context.read<PrinterSettingsBloc>().add(
                          UpdateCategoryAssignment(
                            category: category,
                            location: value == true ? location : '',
                          ),
                        );
                  },
                  activeColor: Colors.blue,
                  checkColor: Colors.white,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    category,
                    style: GoogleFonts.poppins(
                      fontSize: 3.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              _showResetConfirmationDialog(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Reset to Default',
              style: GoogleFonts.poppins(
                fontSize: 3.5.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Save & Close',
              style: GoogleFonts.poppins(
                fontSize: 3.5.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showResetConfirmationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: Colors.grey[800],
          title: Text(
            'Reset Printer Settings',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            'Are you sure you want to reset all printer settings to default? This action cannot be undone.',
            style: GoogleFonts.poppins(
              color: Colors.white70,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(color: Colors.white70),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                context.read<PrinterSettingsBloc>().add(ResetPrinterSettings());
              },
              child: Text(
                'Reset',
                style: GoogleFonts.poppins(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  // Modern UI Components for Tablet Landscape Layout
  Widget _buildModernSectionHeader(String title, IconData icon,
      {String? subtitle}) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFF334155), width: 1),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                  letterSpacing: -0.5,
                ),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: const Color(0xFF9CA3AF),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModernPrinterGrid(
      BuildContext context, PrinterSettingsState state) {
    final printers = state.settings.availablePrinters;

    if (printers.isEmpty) {
      return Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: const Color(0xFF334155),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFF475569), width: 1),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFF475569),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.print_disabled,
                size: 48,
                color: Colors.white54,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No printers found',
              style: GoogleFonts.inter(
                fontSize: 20,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add printers manually or discover them on your network',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: Colors.white60,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _showAddPrinterDialog(context),
                  icon: const Icon(Icons.add),
                  label: const Text('Add Printer'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6366F1),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: () {
                    context.read<PrinterSettingsBloc>().add(DiscoverPrinters());
                  },
                  icon: const Icon(Icons.radar),
                  label: const Text('Discover'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF059669),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(24),
      itemCount: printers.length,
      itemBuilder: (context, index) {
        final printer = printers[index];
        return _buildModernPrinterCard(context, state, printer, index);
      },
    );
  }

  Widget _buildModernPrinterCard(BuildContext context,
      PrinterSettingsState state, PrinterDevice printer, int index) {
    final defaultLocations = PrinterLocation.getDefaultLocations();
    final allLocations = [...defaultLocations, ...state.customLocations];
    final savedLocation = state.settings.printerAssignments[printer.id];

    // Validate that the saved location still exists in available locations
    final currentLocation = (savedLocation != null &&
            (allLocations.contains(savedLocation) ||
                savedLocation == 'Not assigned'))
        ? savedLocation
        : 'Not assigned';

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF334155),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: printer.isConnected
              ? const Color(0xFF10B981)
              : const Color(0xFF475569),
          width: printer.isConnected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: printer.isConnected
                        ? const Color(0xFF10B981)
                        : const Color(0xFF6B7280),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getPrinterIcon(printer.type),
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        printer.name,
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: const Color(0xFF475569),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              printer.type.toUpperCase(),
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.white70,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: printer.isConnected
                                  ? const Color(0xFF10B981)
                                  : const Color(0xFF6B7280),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            printer.isConnected ? 'Connected' : 'Disconnected',
                            style: GoogleFonts.inter(
                              fontSize: 14,
                              color: printer.isConnected
                                  ? const Color(0xFF10B981)
                                  : Colors.white60,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                _buildConnectionButton(context, printer),
              ],
            ),
            if (printer.ipAddress != null || printer.macAddress != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF475569),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getPrinterDetails(printer),
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white70,
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ],
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  'Assign to:',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: const Color(0xFF475569),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: const Color(0xFF6B7280)),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: currentLocation,
                        dropdownColor: const Color(0xFF475569),
                        style: GoogleFonts.inter(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                        icon:
                            const Icon(Icons.expand_more, color: Colors.white),
                        items: ['Not assigned', ...allLocations]
                            .map((location) => DropdownMenuItem<String>(
                                  value: location,
                                  child: Text(location),
                                ))
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            context.read<PrinterSettingsBloc>().add(
                                  UpdatePrinterAssignment(
                                    printerId: printer.id,
                                    location:
                                        value == 'Not assigned' ? '' : value,
                                  ),
                                );
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernCategoriesGrid(
      BuildContext context, PrinterSettingsState state) {
    final defaultLocations = PrinterLocation.getDefaultLocations();
    final allLocations = [...defaultLocations, ...state.customLocations];
    final availableCategories = [
      'Food',
      'Beverages',
      'Desserts',
      'Appetizers',
      'Mains',
      'Drinks',
      'Snacks',
      'Specials'
    ];

    // Show empty state if no locations are available
    if (allLocations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: const Color(0xFF334155),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: const Color(0xFF475569)),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.location_off,
                    size: 48,
                    color: Color(0xFF6B7280),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No Locations Available',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Add custom locations first to assign categories',
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: const Color(0xFF9CA3AF),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => _showAddLocationDialog(context),
                    icon: const Icon(Icons.add_location),
                    label: const Text('Add Location'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6366F1),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(24),
      itemCount: allLocations.length,
      itemBuilder: (context, index) {
        final location = allLocations[index];
        final categoriesForLocation =
            state.settings.categoryAssignments[location] ?? [];

        return Container(
          margin: const EdgeInsets.only(bottom: 20),
          decoration: BoxDecoration(
            color: const Color(0xFF334155),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: const Color(0xFF475569), width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: const Color(0xFF6366F1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.location_on,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      location,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: availableCategories.map((category) {
                    final isAssigned = categoriesForLocation.contains(category);
                    return GestureDetector(
                      onTap: () {
                        context.read<PrinterSettingsBloc>().add(
                              UpdateCategoryAssignment(
                                category: category,
                                location: isAssigned ? '' : location,
                              ),
                            );
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: isAssigned
                              ? const Color(0xFF10B981)
                              : const Color(0xFF475569),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isAssigned
                                ? const Color(0xFF10B981)
                                : const Color(0xFF6B7280),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (isAssigned)
                              const Icon(
                                Icons.check_circle,
                                color: Colors.white,
                                size: 16,
                              ),
                            if (isAssigned) const SizedBox(width: 6),
                            Text(
                              category,
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildModernActionButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                context.read<PrinterSettingsBloc>().add(ResetPrinterSettings());
              },
              icon: const Icon(Icons.refresh, size: 20),
              label: Text(
                'Reset Settings',
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6B7280),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                // Save settings action
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Settings saved successfully!',
                      style: GoogleFonts.inter(color: Colors.white),
                    ),
                    backgroundColor: const Color(0xFF10B981),
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.save, size: 20),
              label: Text(
                'Save Settings',
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF10B981),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddLocationDialog(BuildContext context) {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1E293B),
          title: Text(
            'Add Custom Location',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: TextField(
            controller: controller,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'Enter location name',
              hintStyle: TextStyle(color: Colors.grey[400]),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.grey[600]!),
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: const BorderSide(color: Color(0xFF6366F1)),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(color: Colors.grey[400]),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                final locationName = controller.text.trim();
                if (locationName.isNotEmpty) {
                  context.read<PrinterSettingsBloc>().add(
                        AddCustomLocation(locationName: locationName),
                      );
                  Navigator.of(dialogContext).pop();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6366F1),
                foregroundColor: Colors.white,
              ),
              child: Text(
                'Add',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showAddPrinterDialog(BuildContext context) {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController ipController = TextEditingController();
    final TextEditingController portController = TextEditingController();
    final TextEditingController deviceIdController = TextEditingController();
    final TextEditingController macController = TextEditingController();

    String selectedType = 'thermal';

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: const Color(0xFF1E293B),
              title: Text(
                'Add Printer',
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: nameController,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        labelText: 'Printer Name',
                        labelStyle: TextStyle(color: Colors.grey[400]),
                        hintText: 'Enter printer name',
                        hintStyle: TextStyle(color: Colors.grey[400]),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey[600]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide:
                              const BorderSide(color: Color(0xFF6366F1)),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: selectedType,
                      style: const TextStyle(color: Colors.white),
                      dropdownColor: const Color(0xFF1E293B),
                      decoration: InputDecoration(
                        labelText: 'Printer Type',
                        labelStyle: TextStyle(color: Colors.grey[400]),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey[600]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide:
                              const BorderSide(color: Color(0xFF6366F1)),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      items: ['thermal', 'bluetooth', 'network', 'usb']
                          .map((type) => DropdownMenuItem(
                                value: type,
                                child: Text(type.toUpperCase()),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedType = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    if (selectedType == 'network') ...[
                      TextField(
                        controller: ipController,
                        style: const TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          labelText: 'IP Address',
                          labelStyle: TextStyle(color: Colors.grey[400]),
                          hintText: '*************',
                          hintStyle: TextStyle(color: Colors.grey[400]),
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey[600]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide:
                                const BorderSide(color: Color(0xFF6366F1)),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: portController,
                        style: const TextStyle(color: Colors.white),
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: 'Port',
                          labelStyle: TextStyle(color: Colors.grey[400]),
                          hintText: '9100',
                          hintStyle: TextStyle(color: Colors.grey[400]),
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey[600]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide:
                                const BorderSide(color: Color(0xFF6366F1)),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                    if (selectedType == 'bluetooth') ...[
                      TextField(
                        controller: macController,
                        style: const TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          labelText: 'MAC Address',
                          labelStyle: TextStyle(color: Colors.grey[400]),
                          hintText: '00:11:22:33:44:55',
                          hintStyle: TextStyle(color: Colors.grey[400]),
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey[600]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide:
                                const BorderSide(color: Color(0xFF6366F1)),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                    if (selectedType == 'usb') ...[
                      TextField(
                        controller: deviceIdController,
                        style: const TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          labelText: 'Device ID',
                          labelStyle: TextStyle(color: Colors.grey[400]),
                          hintText: 'USB001',
                          hintStyle: TextStyle(color: Colors.grey[400]),
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey[600]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide:
                                const BorderSide(color: Color(0xFF6366F1)),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(color: Colors.grey[400]),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    final name = nameController.text.trim();
                    if (name.isNotEmpty) {
                      context.read<PrinterSettingsBloc>().add(
                            AddManualPrinter(
                              name: name,
                              type: selectedType,
                              ipAddress: selectedType == 'network'
                                  ? ipController.text.trim()
                                  : null,
                              port: selectedType == 'network' &&
                                      portController.text.isNotEmpty
                                  ? int.tryParse(portController.text.trim())
                                  : null,
                              deviceId: selectedType == 'usb'
                                  ? deviceIdController.text.trim()
                                  : null,
                              macAddress: selectedType == 'bluetooth'
                                  ? macController.text.trim()
                                  : null,
                            ),
                          );
                      Navigator.of(dialogContext).pop();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6366F1),
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    'Add',
                    style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
