import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'dart:ui' as ui;
import '../../widgets/app_bar.dart';
import '../../widgets/tiled_background.dart';
import '../../widgets/calendar_reservations_view.dart';
import '../../widgets/reservation_action_buttons.dart';
import '../../models/reservation_response_model.dart';
import '../../services/table_reservation_service.dart';

class ReservationsManagementPage extends StatefulWidget {
  const ReservationsManagementPage({super.key});

  @override
  State<ReservationsManagementPage> createState() =>
      _ReservationsManagementPageState();
}

class _ReservationsManagementPageState
    extends State<ReservationsManagementPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  ReservationsApiResponse? _reservationsData;
  bool _isLoading = true;
  String? _error;
  DateTime _selectedDate = DateTime.now();
  bool _showMonthReservations = false;
  bool _isGridView = true;
  bool _isCalendarView = false;
  String _filterStatus = 'All';

  final List<String> _statusFilters = [
    'All',
    'Confirmed',
    'Pending',
    'Arrived',
    'Cancelled',
    'No Show',
  ];

  @override
  void initState() {
    super.initState();
    _fetchReservations();
  }

  Future<void> _fetchReservations() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await TableReservationService.getAllReservations(
        date: _selectedDate,
        includeMonth: true,
      );

      setState(() {
        _reservationsData = response;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load reservations: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            datePickerTheme: DatePickerThemeData(
              backgroundColor: Colors.grey.shade900,
              headerBackgroundColor: Colors.blue.shade600,
              headerForegroundColor: Colors.white,
              dayForegroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return Colors.white;
                }
                return Colors.white;
              }),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _fetchReservations();
    }
  }

  List<ReservationItem> get _filteredReservations {
    if (_reservationsData == null) return [];

    final reservationsToShow = _showMonthReservations
        ? _reservationsData!.data.reservationsInMonth
        : _reservationsData!.data.reservationsInCurrentWeek;

    if (_filterStatus == 'All') {
      return reservationsToShow;
    }

    return reservationsToShow
        .where((reservation) =>
            reservation.status.toLowerCase() == _filterStatus.toLowerCase())
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return Scaffold(
      key: _scaffoldKey,
      extendBodyBehindAppBar: true,
      appBar: WaiterAppBar(scaffoldKey: _scaffoldKey),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showCreateReservationDialog,
        backgroundColor: Colors.blue.shade600,
        icon: const Icon(Icons.add, color: Colors.white),
        label: Text(
          'New Reservation',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Stack(
        children: [
          const TiledBackground(),
          BackdropFilter(
            filter: ui.ImageFilter.blur(sigmaX: 12, sigmaY: 12),
            child: SafeArea(
              child: Column(
                children: [
                  _buildHeader(isLandscape),
                  _buildControls(),
                  Expanded(
                    child: _buildContent(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(bool isLandscape) {
    return Container(
      padding: EdgeInsets.all(isLandscape ? 16 : 20),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.event_note,
              color: Colors.blue.shade600,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Reservations Management',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: isLandscape ? 24 : 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Manage table reservations and bookings',
                  style: GoogleFonts.poppins(
                    color: Colors.white70,
                    fontSize: isLandscape ? 14 : 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Column(
        children: [
          // Date and refresh controls
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: _selectDate,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade600.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade600),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          color: Colors.blue.shade400,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          DateFormat('MMM dd, yyyy').format(_selectedDate),
                          style: GoogleFonts.poppins(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: Icon(
                  Icons.refresh,
                  color: Colors.blue.shade400,
                ),
                onPressed: _fetchReservations,
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Filter controls
          Row(
            children: [
              // Month/Week toggle
              Expanded(
                child: Row(
                  children: [
                    Checkbox(
                      value: _showMonthReservations,
                      onChanged: (val) {
                        setState(() {
                          _showMonthReservations = val ?? false;
                        });
                      },
                      activeColor: Colors.blue.shade600,
                      checkColor: Colors.white,
                    ),
                    Text(
                      'Show month',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              // Status filter
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border:
                      Border.all(color: Colors.white.withValues(alpha: 0.2)),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _filterStatus,
                    dropdownColor: Colors.grey.shade800,
                    style:
                        GoogleFonts.poppins(color: Colors.white, fontSize: 14),
                    icon: const Icon(Icons.arrow_drop_down,
                        color: Colors.white, size: 20),
                    items: _statusFilters.map((String status) {
                      return DropdownMenuItem<String>(
                        value: status,
                        child: Text(status),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          _filterStatus = newValue;
                        });
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // View toggle
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border:
                      Border.all(color: Colors.white.withValues(alpha: 0.2)),
                ),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => setState(() {
                        _isGridView = true;
                        _isCalendarView = false;
                      }),
                      icon: Icon(
                        Icons.grid_view,
                        color: _isGridView && !_isCalendarView
                            ? Colors.blue.shade400
                            : Colors.white70,
                        size: 20,
                      ),
                    ),
                    IconButton(
                      onPressed: () => setState(() {
                        _isGridView = false;
                        _isCalendarView = false;
                      }),
                      icon: Icon(
                        Icons.list,
                        color: !_isGridView && !_isCalendarView
                            ? Colors.blue.shade400
                            : Colors.white70,
                        size: 20,
                      ),
                    ),
                    IconButton(
                      onPressed: () => setState(() {
                        _isCalendarView = true;
                        _isGridView = false;
                      }),
                      icon: Icon(
                        Icons.calendar_view_month,
                        color: _isCalendarView
                            ? Colors.blue.shade400
                            : Colors.white70,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.blue),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red.shade400,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: GoogleFonts.poppins(
                color: Colors.red.shade400,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchReservations,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
              ),
              child: Text(
                'Retry',
                style: GoogleFonts.poppins(color: Colors.white),
              ),
            ),
          ],
        ),
      );
    }

    final filteredReservations = _filteredReservations;

    if (filteredReservations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_busy,
              size: 64,
              color: Colors.white.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No reservations found',
              style: GoogleFonts.poppins(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'for ${DateFormat('MMM dd, yyyy').format(_selectedDate)}',
              style: GoogleFonts.poppins(
                color: Colors.white.withValues(alpha: 0.5),
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    if (_isCalendarView) {
      return CalendarReservationsView(
        reservations: filteredReservations,
        selectedDate: _selectedDate,
        onDateSelected: (date) {
          setState(() {
            _selectedDate = date;
          });
          _fetchReservations();
        },
        onReservationUpdated: _fetchReservations,
      );
    }

    return _isGridView
        ? _buildGridView(filteredReservations)
        : _buildListView(filteredReservations);
  }

  Widget _buildGridView(List<ReservationItem> reservations) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return GridView.builder(
      padding: const EdgeInsets.all(20),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: isLandscape ? 5 : 2,
        childAspectRatio: isLandscape ? 1.1 : 0.9,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: reservations.length,
      itemBuilder: (context, index) {
        return _buildReservationCard(reservations[index], true);
      },
    );
  }

  Widget _buildListView(List<ReservationItem> reservations) {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: reservations.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildReservationCard(reservations[index], false),
        );
      },
    );
  }

  Widget _buildReservationCard(ReservationItem reservation, bool isGridView) {
    final isToday =
        DateUtils.isSameDay(reservation.reservationTime, DateTime.now());

    Color statusColor;
    IconData statusIcon;

    switch (reservation.status.toLowerCase()) {
      case 'confirmed':
        statusColor = Colors.green.shade400;
        statusIcon = Icons.check_circle;
        break;
      case 'arrived':
        statusColor = Colors.blue.shade400;
        statusIcon = Icons.person;
        break;
      case 'cancelled':
        statusColor = Colors.red.shade400;
        statusIcon = Icons.cancel;
        break;
      default:
        statusColor = Colors.orange.shade400;
        statusIcon = Icons.schedule;
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isToday
              ? Colors.blue.shade600
              : Colors.white.withValues(alpha: 0.1),
          width: isToday ? 2 : 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: isGridView
                ? _buildGridCardContent(
                    reservation, statusColor, statusIcon, isToday)
                : _buildListCardContent(
                    reservation, statusColor, statusIcon, isToday),
          ),
        ),
      ),
    );
  }

  Widget _buildGridCardContent(ReservationItem reservation, Color statusColor,
      IconData statusIcon, bool isToday) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: statusColor.withValues(alpha: 0.5)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(statusIcon, color: statusColor, size: 12),
                  const SizedBox(width: 4),
                  Text(
                    reservation.status,
                    style: GoogleFonts.poppins(
                      color: statusColor,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(),
            if (isToday)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue.shade600.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'TODAY',
                  style: GoogleFonts.poppins(
                    color: Colors.blue.shade400,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          reservation.customerName,
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          'Table ${reservation.table.name}',
          style: GoogleFonts.poppins(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(Icons.people, color: Colors.white70, size: 14),
            const SizedBox(width: 4),
            Text(
              '${reservation.numberOfGuests} guests',
              style: GoogleFonts.poppins(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(Icons.access_time, color: Colors.white70, size: 14),
            const SizedBox(width: 4),
            Text(
              DateFormat('HH:mm').format(reservation.reservationTime),
              style: GoogleFonts.poppins(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
        const Spacer(),
        if (reservation.phoneNumber.isNotEmpty)
          Text(
            reservation.phoneNumber,
            style: GoogleFonts.poppins(
              color: Colors.blue.shade400,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        const SizedBox(height: 8),
        ReservationActionButtons(
          reservationId: reservation.reservationId,
          status: reservation.status,
          onActionCompleted: _fetchReservations,
        ),
      ],
    );
  }

  Widget _buildListCardContent(ReservationItem reservation, Color statusColor,
      IconData statusIcon, bool isToday) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: statusColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                              color: statusColor.withValues(alpha: 0.5)),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(statusIcon, color: statusColor, size: 12),
                            const SizedBox(width: 4),
                            Text(
                              reservation.status,
                              style: GoogleFonts.poppins(
                                color: statusColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (isToday) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade600.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'TODAY',
                            style: GoogleFonts.poppins(
                              color: Colors.blue.shade400,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    reservation.customerName,
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'Table ${reservation.table.name}',
                    style: GoogleFonts.poppins(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  DateFormat('MMM dd').format(reservation.reservationTime),
                  style: GoogleFonts.poppins(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
                Text(
                  DateFormat('HH:mm').format(reservation.reservationTime),
                  style: GoogleFonts.poppins(
                    color: Colors.blue.shade400,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Icon(Icons.people, color: Colors.white70, size: 16),
            const SizedBox(width: 4),
            Text(
              '${reservation.numberOfGuests} guests',
              style: GoogleFonts.poppins(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
            const Spacer(),
            if (reservation.phoneNumber.isNotEmpty)
              Text(
                reservation.phoneNumber,
                style: GoogleFonts.poppins(
                  color: Colors.blue.shade400,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
          ],
        ),
        if (reservation.specialNotes != null &&
            reservation.specialNotes!.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            'Notes: ${reservation.specialNotes}',
            style: GoogleFonts.poppins(
              color: Colors.white60,
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
        const SizedBox(height: 12),
        ReservationActionButtons(
          reservationId: reservation.reservationId,
          status: reservation.status,
          onActionCompleted: _fetchReservations,
        ),
      ],
    );
  }

  void _showCreateReservationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey.shade900,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          'Create New Reservation',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'This feature will allow you to create new table reservations with customer details and special requirements.',
          style: GoogleFonts.poppins(
            color: Colors.white70,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to dine-in page for table selection
              // context.goNamed(RouterConstants.dineIn);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
            ),
            child: Text(
              'Select Table',
              style: GoogleFonts.poppins(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
