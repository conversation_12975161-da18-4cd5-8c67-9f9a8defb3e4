import 'package:flutter/foundation.dart';
import '../models/bill_modification_state.dart';

/// Service to manage bill modifications state across the app
class BillModificationService extends ChangeNotifier {
  static final BillModificationService _instance = BillModificationService._internal();
  factory BillModificationService() => _instance;
  BillModificationService._internal();

  BillModificationState _state = const BillModificationState();

  /// Get current state
  BillModificationState get state => _state;

  /// Get current tip amount
  double get tipAmount => _state.tipAmount;

  /// Get current discount amount
  double get discountAmount => _state.discountAmount;

  /// Get current redeem code
  String get redeemCode => _state.redeemCode;

  /// Check if manager approval is required
  bool get requiresManagerApproval => _state.requiresManagerApproval;

  /// Check if manager has approved
  bool get isManagerApproved => _state.isManagerApproved;

  /// Check if there are any modifications
  bool get hasModifications => _state.hasModifications;

  /// Update tip amount
  void updateTipAmount(double amount) {
    _state = _state.copyWith(
      tipAmount: amount,
      lastModified: DateTime.now(),
    );
    notifyListeners();
  }

  /// Update discount amount
  void updateDiscountAmount(double amount) {
    _state = _state.copyWith(
      discountAmount: amount,
      lastModified: DateTime.now(),
      // Reset manager approval if discount changes
      isManagerApproved: amount <= 0 ? false : _state.isManagerApproved,
    );
    notifyListeners();
  }

  /// Update redeem code
  void updateRedeemCode(String code) {
    _state = _state.copyWith(
      redeemCode: code,
      lastModified: DateTime.now(),
    );
    notifyListeners();
  }

  /// Set manager approval status
  void setManagerApproval(bool approved) {
    _state = _state.copyWith(
      isManagerApproved: approved,
      lastModified: DateTime.now(),
    );
    notifyListeners();
  }

  /// Update all values at once
  void updateAll({
    double? tipAmount,
    double? discountAmount,
    String? redeemCode,
    bool? isManagerApproved,
  }) {
    _state = _state.copyWith(
      tipAmount: tipAmount,
      discountAmount: discountAmount,
      redeemCode: redeemCode,
      isManagerApproved: isManagerApproved,
      lastModified: DateTime.now(),
    );
    notifyListeners();
  }

  /// Reset all modifications
  void reset() {
    _state = const BillModificationState();
    notifyListeners();
  }

  /// Get data for cart confirmation
  Map<String, dynamic> getCartConfirmationData() {
    return _state.toJson();
  }

  /// Check if ready for submission
  bool isReadyForSubmission() {
    return _state.isValidForSubmission;
  }
}
