import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../config/env_config.dart';
import '../utils/http_client.dart';

/// Service for handling promotion-related functionality including coupons, tips, vouchers, and OTP verification
class PromotionService {
  static String get _baseUrl => EnvConfig.apiBaseUrl;

  /// Apply a coupon code to an order (requires OTP verification)
  /// This method initiates the coupon application process
  static Future<PromotionResult> applyCoupon({
    required String orderDetailId,
    required String couponCode,
  }) async {
    try {
      debugPrint('🎫 PromotionService: Applying coupon $couponCode to order $orderDetailId');
      
      if (couponCode.trim().isEmpty) {
        return PromotionResult.error('Please enter a coupon code');
      }

      // For coupons, we need OTP verification first
      // Return success to indicate OTP modal should be shown
      return PromotionResult.success(
        message: 'Ready for OTP verification',
        requiresOTP: true,
        code: couponCode,
        type: 'coupon',
      );
    } catch (e) {
      debugPrint('🎫 PromotionService: Error applying coupon: $e');
      return PromotionResult.error('Error applying coupon: $e');
    }
  }

  /// Apply a tip code to an order (direct application, no OTP required)
  static Future<PromotionResult> applyTip({
    required String orderDetailId,
    required String tipCode,
  }) async {
    try {
      debugPrint('💰 PromotionService: Applying tip $tipCode to order $orderDetailId');
      
      if (tipCode.trim().isEmpty) {
        return PromotionResult.error('Please enter a tip code');
      }

      final response = await http.put(
        Uri.parse('$_baseUrl/promotion/redeem/tips/$orderDetailId'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'tips': [tipCode]
        }),
      );

      debugPrint('💰 PromotionService: Tip application response: ${response.statusCode}');

      if (response.statusCode == 200) {
        return PromotionResult.success(
          message: 'Tip "$tipCode" applied successfully!',
          code: tipCode,
          type: 'tip',
        );
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to apply tip');
      }
    } catch (e) {
      debugPrint('💰 PromotionService: Error applying tip: $e');
      return PromotionResult.error('Error applying tip: $e');
    }
  }

  /// Apply a voucher code to an order (requires OTP verification)
  /// This method initiates the voucher application process
  static Future<PromotionResult> applyVoucher({
    required String orderDetailId,
    required String voucherCode,
  }) async {
    try {
      debugPrint('🎁 PromotionService: Applying voucher $voucherCode to order $orderDetailId');
      
      if (voucherCode.trim().isEmpty) {
        return PromotionResult.error('Please enter a voucher code');
      }

      // For vouchers, we need OTP verification first
      // Return success to indicate OTP modal should be shown
      return PromotionResult.success(
        message: 'Ready for OTP verification',
        requiresOTP: true,
        code: voucherCode,
        type: 'voucher',
      );
    } catch (e) {
      debugPrint('🎁 PromotionService: Error applying voucher: $e');
      return PromotionResult.error('Error applying voucher: $e');
    }
  }

  /// Verify OTP for coupon or voucher application
  static Future<PromotionResult> verifyOTP({
    required String orderDetailId,
    required String code,
    required String type, // 'coupon' or 'voucher'
    required String otp,
  }) async {
    try {
      debugPrint('🔐 PromotionService: Verifying OTP for $type: $code');
      
      if (otp.length != 5) {
        return PromotionResult.error('Please enter a valid 5-digit OTP');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/promotion/verify-otp'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'orderDetailId': orderDetailId,
          'code': code,
          'type': type,
          'otp': otp,
        }),
      );

      debugPrint('🔐 PromotionService: OTP verification response: ${response.statusCode}');

      if (response.statusCode == 200) {
        // OTP verified successfully, now apply the code
        return await _applyCodeAfterOTPVerification(
          orderDetailId: orderDetailId,
          code: code,
          type: type,
        );
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Invalid OTP');
      }
    } catch (e) {
      debugPrint('🔐 PromotionService: Error verifying OTP: $e');
      return PromotionResult.error('Error verifying OTP: $e');
    }
  }

  /// Apply the code after successful OTP verification
  static Future<PromotionResult> _applyCodeAfterOTPVerification({
    required String orderDetailId,
    required String code,
    required String type,
  }) async {
    try {
      debugPrint('✅ PromotionService: Applying $type after OTP verification: $code');

      String endpoint;
      String successMessage;

      if (type == 'coupon') {
        endpoint = '$_baseUrl/promotion/redeem/coupons/$orderDetailId';
        successMessage = 'Coupon "$code" applied successfully!';
      } else if (type == 'voucher') {
        endpoint = '$_baseUrl/promotion/redeem/vouchers/$orderDetailId';
        successMessage = 'Voucher "$code" applied successfully!';
      } else {
        throw Exception('Invalid code type: $type');
      }

      final response = await http.put(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          '${type}s': [code]
        }),
      );

      debugPrint('✅ PromotionService: Code application response: ${response.statusCode}');

      if (response.statusCode == 200) {
        return PromotionResult.success(
          message: successMessage,
          code: code,
          type: type,
        );
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to apply $type');
      }
    } catch (e) {
      debugPrint('✅ PromotionService: Error applying code after OTP: $e');
      return PromotionResult.error('Error applying $type: $e');
    }
  }

  // ===== NEW API METHODS AS PER REQUIREMENTS =====

  /// Redeem coupons for an order (NEW API)
  /// POST {{LAMBDA_HOST}}/promotion/redeem/coupons/:orderDetailId
  static Future<Map<String, dynamic>?> redeemCoupons({
    required String orderDetailId,
    required List<String> coupons,
  }) async {
    try {
      final url = '$_baseUrl/promotion/redeem/coupons/$orderDetailId';
      debugPrint('🎫 PromotionService: Redeeming coupons for order $orderDetailId');
      debugPrint('🎫 PromotionService: Coupons: $coupons');

      final requestBody = {
        'coupons': coupons,
      };

      final response = await HttpClientService.post(
        url,
        body: jsonEncode(requestBody),
      );

      debugPrint('🎫 PromotionService: Response status: ${response.statusCode}');
      debugPrint('🎫 PromotionService: Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        debugPrint('✅ PromotionService: Coupons redeemed successfully');
        return responseData;
      } else {
        debugPrint('❌ PromotionService: Failed to redeem coupons: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ PromotionService: Error redeeming coupons: $e');
      return null;
    }
  }

  /// Confirm coupon redemption with OTP (NEW API)
  /// POST {{LAMBDA_HOST}}/promotion/redeem/coupons/confirm-redemption/:orderDetailId
  static Future<Map<String, dynamic>?> confirmCouponRedemption({
    required String orderDetailId,
    required String otp,
  }) async {
    try {
      final url = '$_baseUrl/promotion/redeem/coupons/confirm-redemption/$orderDetailId';
      debugPrint('🎫 PromotionService: Confirming coupon redemption for order $orderDetailId with OTP');

      final requestBody = {
        'otp': otp,
      };

      final response = await HttpClientService.post(
        url,
        body: jsonEncode(requestBody),
      );

      debugPrint('🎫 PromotionService: Response status: ${response.statusCode}');
      debugPrint('🎫 PromotionService: Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        debugPrint('✅ PromotionService: Coupon redemption confirmed successfully');
        return responseData;
      } else {
        debugPrint('❌ PromotionService: Failed to confirm coupon redemption: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ PromotionService: Error confirming coupon redemption: $e');
      return null;
    }
  }

  /// Redeem vouchers for an order (NEW API)
  /// POST {{LAMBDA_HOST}}/promotion/redeem/vouchers/:orderDetailId
  static Future<Map<String, dynamic>?> redeemVouchers({
    required String orderDetailId,
    required List<String> vouchers,
  }) async {
    try {
      final url = '$_baseUrl/promotion/redeem/vouchers/$orderDetailId';
      debugPrint('🎟️ PromotionService: Redeeming vouchers for order $orderDetailId');
      debugPrint('🎟️ PromotionService: Vouchers: $vouchers');

      final requestBody = {
        'vouchers': vouchers,
      };

      final response = await HttpClientService.post(
        url,
        body: jsonEncode(requestBody),
      );

      debugPrint('🎟️ PromotionService: Response status: ${response.statusCode}');
      debugPrint('🎟️ PromotionService: Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        debugPrint('✅ PromotionService: Vouchers redeemed successfully');
        return responseData;
      } else {
        debugPrint('❌ PromotionService: Failed to redeem vouchers: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ PromotionService: Error redeeming vouchers: $e');
      return null;
    }
  }

  /// Confirm voucher redemption with OTP (NEW API)
  /// POST {{LAMBDA_HOST}}/promotion/redeem/vouchers/confirm-redemption/:orderDetailId
  static Future<Map<String, dynamic>?> confirmVoucherRedemption({
    required String orderDetailId,
    required String otp,
  }) async {
    try {
      final url = '$_baseUrl/promotion/redeem/vouchers/confirm-redemption/$orderDetailId';
      debugPrint('🎟️ PromotionService: Confirming voucher redemption for order $orderDetailId with OTP');

      final requestBody = {
        'otp': otp,
      };

      final response = await HttpClientService.post(
        url,
        body: jsonEncode(requestBody),
      );

      debugPrint('🎟️ PromotionService: Response status: ${response.statusCode}');
      debugPrint('🎟️ PromotionService: Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        debugPrint('✅ PromotionService: Voucher redemption confirmed successfully');
        return responseData;
      } else {
        debugPrint('❌ PromotionService: Failed to confirm voucher redemption: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ PromotionService: Error confirming voucher redemption: $e');
      return null;
    }
  }

  /// Resend OTP for promotion redemption (NEW API)
  /// POST {{LAMBDA_HOST}}/promotion/redeem/resend-otp/:orderDetailId
  static Future<bool> resendOtp({
    required String orderDetailId,
  }) async {
    try {
      final url = '$_baseUrl/promotion/redeem/resend-otp/$orderDetailId';
      debugPrint('📱 PromotionService: Resending OTP for order $orderDetailId');

      final response = await HttpClientService.post(url);

      debugPrint('📱 PromotionService: Response status: ${response.statusCode}');
      debugPrint('📱 PromotionService: Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        debugPrint('✅ PromotionService: OTP resent successfully');
        return true;
      } else {
        debugPrint('❌ PromotionService: Failed to resend OTP: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ PromotionService: Error resending OTP: $e');
      return false;
    }
  }
}

/// Result class for promotion operations
class PromotionResult {
  final bool success;
  final String message;
  final String? code;
  final String? type;
  final bool requiresOTP;

  PromotionResult._({
    required this.success,
    required this.message,
    this.code,
    this.type,
    this.requiresOTP = false,
  });

  factory PromotionResult.success({
    required String message,
    String? code,
    String? type,
    bool requiresOTP = false,
  }) {
    return PromotionResult._(
      success: true,
      message: message,
      code: code,
      type: type,
      requiresOTP: requiresOTP,
    );
  }

  factory PromotionResult.error(String message) {
    return PromotionResult._(
      success: false,
      message: message,
    );
  }
}

/// State management class for promotion functionality
class PromotionState {
  final List<String> appliedCoupons;
  final List<String> appliedTips;
  final List<String> appliedVouchers;
  final bool isApplyingCoupon;
  final bool isApplyingTip;
  final bool isApplyingVoucher;
  final bool isVerifyingOTP;
  final String? pendingCode;
  final String? pendingType;

  PromotionState({
    this.appliedCoupons = const [],
    this.appliedTips = const [],
    this.appliedVouchers = const [],
    this.isApplyingCoupon = false,
    this.isApplyingTip = false,
    this.isApplyingVoucher = false,
    this.isVerifyingOTP = false,
    this.pendingCode,
    this.pendingType,
  });

  PromotionState copyWith({
    List<String>? appliedCoupons,
    List<String>? appliedTips,
    List<String>? appliedVouchers,
    bool? isApplyingCoupon,
    bool? isApplyingTip,
    bool? isApplyingVoucher,
    bool? isVerifyingOTP,
    String? pendingCode,
    String? pendingType,
  }) {
    return PromotionState(
      appliedCoupons: appliedCoupons ?? this.appliedCoupons,
      appliedTips: appliedTips ?? this.appliedTips,
      appliedVouchers: appliedVouchers ?? this.appliedVouchers,
      isApplyingCoupon: isApplyingCoupon ?? this.isApplyingCoupon,
      isApplyingTip: isApplyingTip ?? this.isApplyingTip,
      isApplyingVoucher: isApplyingVoucher ?? this.isApplyingVoucher,
      isVerifyingOTP: isVerifyingOTP ?? this.isVerifyingOTP,
      pendingCode: pendingCode ?? this.pendingCode,
      pendingType: pendingType ?? this.pendingType,
    );
  }

  /// Check if a coupon is already applied
  bool isCouponApplied(String couponCode) {
    return appliedCoupons.contains(couponCode);
  }

  /// Check if a tip is already applied
  bool isTipApplied(String tipCode) {
    return appliedTips.contains(tipCode);
  }

  /// Check if a voucher is already applied
  bool isVoucherApplied(String voucherCode) {
    return appliedVouchers.contains(voucherCode);
  }

  /// Add a successfully applied coupon
  PromotionState addAppliedCoupon(String couponCode) {
    final newCoupons = List<String>.from(appliedCoupons);
    if (!newCoupons.contains(couponCode)) {
      newCoupons.add(couponCode);
    }
    return copyWith(appliedCoupons: newCoupons);
  }

  /// Add a successfully applied tip
  PromotionState addAppliedTip(String tipCode) {
    final newTips = List<String>.from(appliedTips);
    if (!newTips.contains(tipCode)) {
      newTips.add(tipCode);
    }
    return copyWith(appliedTips: newTips);
  }

  /// Add a successfully applied voucher
  PromotionState addAppliedVoucher(String voucherCode) {
    final newVouchers = List<String>.from(appliedVouchers);
    if (!newVouchers.contains(voucherCode)) {
      newVouchers.add(voucherCode);
    }
    return copyWith(appliedVouchers: newVouchers);
  }

  /// Clear pending OTP state
  PromotionState clearPendingOTP() {
    return copyWith(
      pendingCode: null,
      pendingType: null,
      isVerifyingOTP: false,
    );
  }

  /// Set pending OTP state
  PromotionState setPendingOTP(String code, String type) {
    return copyWith(
      pendingCode: code,
      pendingType: type,
    );
  }
}
