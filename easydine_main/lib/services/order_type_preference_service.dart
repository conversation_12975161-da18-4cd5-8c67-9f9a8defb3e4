import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../models/customization_models.dart';
import 'customization_service.dart';

/// Service for managing order type preferences using SharedPreferences
/// This service handles storing and retrieving the current order type ID
/// when switching between different POS modes (dine-in, delivery, takeaway, etc.)
class OrderTypePreferenceService {
  static const String _currentOrderTypeIdKey = 'current_order_type_id';
  static const String _currentOrderTypeNameKey = 'current_order_type_name';
  
  /// Store the current order type ID and name in SharedPreferences
  static Future<bool> setCurrentOrderType(String orderTypeId, String orderTypeName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      debugPrint('🔧 OrderTypePreferenceService: Storing order type - ID: $orderTypeId, Name: $orderTypeName');
      
      final idResult = await prefs.setString(_currentOrderTypeIdKey, orderTypeId);
      final nameResult = await prefs.setString(_currentOrderTypeNameKey, orderTypeName);
      
      if (idResult && nameResult) {
        debugPrint('✅ OrderTypePreferenceService: Successfully stored order type preferences');
        return true;
      } else {
        debugPrint('❌ OrderTypePreferenceService: Failed to store order type preferences');
        return false;
      }
    } catch (e) {
      debugPrint('❌ OrderTypePreferenceService: Error storing order type: $e');
      return false;
    }
  }
  
  /// Get the current order type ID from SharedPreferences
  /// If no order type is stored, attempts to set a default one
  static Future<String?> getCurrentOrderTypeId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? orderTypeId = prefs.getString(_currentOrderTypeIdKey);

      // If no order type is stored, try to set a default one
      if (orderTypeId == null || orderTypeId.isEmpty) {
        debugPrint('🔧 OrderTypePreferenceService: No order type stored, attempting to set default...');

        // Try to set "dine in" as default
        final defaultSet = await setOrderTypeByName('dine in');
        if (defaultSet) {
          orderTypeId = prefs.getString(_currentOrderTypeIdKey);
          debugPrint('🔧 OrderTypePreferenceService: Default order type set successfully: $orderTypeId');
        } else {
          debugPrint('❌ OrderTypePreferenceService: Failed to set default order type');
        }
      }

      debugPrint('🔧 OrderTypePreferenceService: Retrieved order type ID: $orderTypeId');
      return orderTypeId;
    } catch (e) {
      debugPrint('❌ OrderTypePreferenceService: Error retrieving order type ID: $e');
      return null;
    }
  }
  
  /// Get the current order type name from SharedPreferences
  static Future<String?> getCurrentOrderTypeName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final orderTypeName = prefs.getString(_currentOrderTypeNameKey);
      
      debugPrint('🔧 OrderTypePreferenceService: Retrieved order type name: $orderTypeName');
      return orderTypeName;
    } catch (e) {
      debugPrint('❌ OrderTypePreferenceService: Error retrieving order type name: $e');
      return null;
    }
  }
  
  /// Get both order type ID and name as a map
  static Future<Map<String, String?>> getCurrentOrderType() async {
    try {
      final orderTypeId = await getCurrentOrderTypeId();
      final orderTypeName = await getCurrentOrderTypeName();
      
      return {
        'id': orderTypeId,
        'name': orderTypeName,
      };
    } catch (e) {
      debugPrint('❌ OrderTypePreferenceService: Error retrieving order type: $e');
      return {
        'id': null,
        'name': null,
      };
    }
  }
  
  /// Find and store order type by name (e.g., "dine in", "takeaway", "delivery")
  /// This method fetches order types from the API and finds the matching one
  static Future<bool> setOrderTypeByName(String orderTypeName) async {
    try {
      debugPrint('🔧 OrderTypePreferenceService: Finding order type for name: $orderTypeName');
      
      // Fetch order types from API
      final orderTypes = await CustomizationService.getOrderTypes();
      if (orderTypes == null || orderTypes.isEmpty) {
        debugPrint('❌ OrderTypePreferenceService: No order types available');
        return false;
      }
      
      // Normalize the input name for comparison
      final normalizedInputName = orderTypeName.toLowerCase().trim();
      
      // Find matching order type
      OrderType? matchingOrderType;
      
      // Try exact match first
      for (final orderType in orderTypes) {
        if (orderType.name.toLowerCase().trim() == normalizedInputName) {
          matchingOrderType = orderType;
          break;
        }
      }
      
      // If no exact match, try partial match
      if (matchingOrderType == null) {
        for (final orderType in orderTypes) {
          final orderTypeLower = orderType.name.toLowerCase().trim();
          
          // Handle common variations
          if ((normalizedInputName.contains('dine') && orderTypeLower.contains('dine')) ||
              (normalizedInputName.contains('takeaway') && orderTypeLower.contains('takeaway')) ||
              (normalizedInputName.contains('take away') && orderTypeLower.contains('takeaway')) ||
              (normalizedInputName.contains('delivery') && orderTypeLower.contains('delivery')) ||
              (normalizedInputName.contains('pickup') && orderTypeLower.contains('pickup'))) {
            matchingOrderType = orderType;
            break;
          }
        }
      }
      
      if (matchingOrderType != null) {
        debugPrint('✅ OrderTypePreferenceService: Found matching order type - ID: ${matchingOrderType.orderTypeId}, Name: ${matchingOrderType.name}');
        return await setCurrentOrderType(matchingOrderType.orderTypeId, matchingOrderType.name);
      } else {
        debugPrint('❌ OrderTypePreferenceService: No matching order type found for: $orderTypeName');
        
        // Log available order types for debugging
        debugPrint('🔧 OrderTypePreferenceService: Available order types:');
        for (final orderType in orderTypes) {
          debugPrint('  - ID: ${orderType.orderTypeId}, Name: ${orderType.name}');
        }
        
        return false;
      }
    } catch (e) {
      debugPrint('❌ OrderTypePreferenceService: Error setting order type by name: $e');
      return false;
    }
  }
  
  /// Clear the stored order type preferences
  static Future<bool> clearCurrentOrderType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final idResult = await prefs.remove(_currentOrderTypeIdKey);
      final nameResult = await prefs.remove(_currentOrderTypeNameKey);
      
      if (idResult && nameResult) {
        debugPrint('✅ OrderTypePreferenceService: Successfully cleared order type preferences');
        return true;
      } else {
        debugPrint('❌ OrderTypePreferenceService: Failed to clear order type preferences');
        return false;
      }
    } catch (e) {
      debugPrint('❌ OrderTypePreferenceService: Error clearing order type: $e');
      return false;
    }
  }
  
  /// Check if an order type is currently stored
  static Future<bool> hasCurrentOrderType() async {
    try {
      final orderTypeId = await getCurrentOrderTypeId();
      return orderTypeId != null && orderTypeId.isNotEmpty;
    } catch (e) {
      debugPrint('❌ OrderTypePreferenceService: Error checking order type existence: $e');
      return false;
    }
  }

  /// Ensure we have a valid order type ID, setting a default if needed
  /// This method guarantees a non-null order type ID for cart operations
  static Future<String> ensureOrderTypeId() async {
    try {
      String? orderTypeId = await getCurrentOrderTypeId();

      if (orderTypeId != null && orderTypeId.isNotEmpty) {
        return orderTypeId;
      }

      // If still no order type, try to fetch the first available order type
      debugPrint('🔧 OrderTypePreferenceService: No order type available, fetching first available...');
      final orderTypes = await CustomizationService.getOrderTypes();

      if (orderTypes != null && orderTypes.isNotEmpty) {
        final firstOrderType = orderTypes.first;
        await setCurrentOrderType(firstOrderType.orderTypeId, firstOrderType.name);
        debugPrint('✅ OrderTypePreferenceService: Set first available order type: ${firstOrderType.name} (${firstOrderType.orderTypeId})');
        return firstOrderType.orderTypeId;
      }

      // If no order types are available at all, this is a critical error
      debugPrint('❌ OrderTypePreferenceService: No order types available from API');
      throw Exception('No order types available - cannot proceed with cart operations');

    } catch (e) {
      debugPrint('❌ OrderTypePreferenceService: Error ensuring order type ID: $e');
      rethrow;
    }
  }
}
