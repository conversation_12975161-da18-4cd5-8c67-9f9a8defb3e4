import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/env_config.dart';
import '../utils/http_client.dart';

/// Service for handling manager-related API calls
class ManagerService {
  static final String _baseUrl = EnvConfig.apiBaseUrl;

  /// Verify manager PIN for discount approval
  /// POST {{HOSTNAME}}:{{PORT}}/staff-manage/verify-manager-pin
  static Future<bool> verifyManagerPin({
    required int pin,
  }) async {
    try {
      final url = '$_baseUrl/staff-manage/verify-manager-pin';
      debugPrint('🔐 ManagerService: Verifying manager PIN at $url');

      // // Ensure PIN is 5 digits
      // if (pin.bitLength != 5) {
      //   debugPrint('❌ ManagerService: PIN must be exactly 5 digits');
      //   return false;
      // }

      final requestBody = {
        'pin': pin,
      };

      debugPrint('🔐 ManagerService: Request body: ${jsonEncode(requestBody)}');

      final response = await HttpClientService.post(
        url,
        body: jsonEncode(requestBody),
      );

      debugPrint('🔐 ManagerService: Response status: ${response.statusCode}');
      debugPrint('🔐 ManagerService: Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        debugPrint('✅ ManagerService: Manager PIN verified successfully');
        return true;
      } else {
        debugPrint('❌ ManagerService: Failed to verify manager PIN: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ ManagerService: Error verifying manager PIN: $e');
      return false;
    }
  }

  /// Validate PIN format (must be 5 digits)
  static bool isValidPinFormat(int pin) {
    final pinString = pin.toString();
    return pinString.length == 5 && int.tryParse(pinString) != null;
  }

  /// Format PIN for display (mask with asterisks)
  static String formatPinForDisplay(String pin) {
    if (pin.isEmpty) return '';
    return '*' * pin.length;
  }
}
