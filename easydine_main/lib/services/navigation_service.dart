import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import '../router/router_constants.dart';

class NavigationService {
  /// Handle back button press - navigate to home instead of closing app
  static Future<bool> handleBackButton(BuildContext context) async {
    try {
      // Check if context is still mounted
      if (!context.mounted) {
        print('🚫 Context not mounted, preventing app close');
        return true;
      }

      final router = GoRouter.of(context);
      final currentLocation =
          router.routerDelegate.currentConfiguration.fullPath;

      print('🔙 Back button pressed on: $currentLocation');

      // Splash page - prevent back navigation (let splash handle its own navigation)
      if (currentLocation == '/') {
        print('🚀 On splash page, back press ignored');
        return true; // Handled - prevent back navigation
      }

      // Authentication pages - try to redirect to home, but handle auth redirects gracefully
      final authPages = ['/login', '/branch-selection', '/pin-entry'];
      if (authPages.contains(currentLocation)) {
        print('🔐 On auth page, attempting to redirect to home');
        try {
          router.go(RouterConstants.home);
        } catch (authError) {
          print('⚠️ Auth redirect failed, staying on current page: $authError');
        }
        return true; // Handled - prevent app minimization
      }

      // Daily checklist page - redirect to home (don't allow back navigation)
      if (currentLocation == '/daily-checklist') {
        print('📋 On daily checklist, redirecting to home');
        try {
          router.go(RouterConstants.home);
        } catch (checklistError) {
          print('⚠️ Checklist redirect failed: $checklistError');
        }
        return true; // Handled - prevent app minimization
      }

      // For all other pages, navigate to home instead of minimizing app
      if (currentLocation != RouterConstants.home &&
          currentLocation != '/home') {
        print('🏠 Navigating to home page from: $currentLocation');
        try {
          router.go(RouterConstants.home);
        } catch (homeError) {
          print('⚠️ Home redirect failed: $homeError');
        }
        return true; // Handled - don't close the app
      }

      // If already on home, do nothing (prevent app close)
      print('🏠 Already on home page, back press ignored');
      return true; // Handled - don't close the app
    } catch (e) {
      print('❌ Error handling back button: $e');
      // On any error, just prevent app from closing
      return true;
    }
  }
}
