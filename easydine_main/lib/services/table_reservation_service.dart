import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/env_config.dart';
import '../utils/http_client.dart';
import '../models/reservation_response_model.dart';

class TableReservationService {
  static final String _baseUrl = EnvConfig.apiBaseUrl;

  /// Create a table reservation
  /// POST {{LAMBDA_HOST}}/table-reservation/tables/:tableId/reservations
  static Future<bool> createReservation({
    required String tableId,
    required String customerName,
    required String phoneNumber,
    required int numberOfGuests,
    required DateTime reservationTime,
    String? specialNotes,
  }) async {
    try {
      final url = '$_baseUrl/table-reservation/tables/$tableId/reservations';
      debugPrint('📅 TableReservationService: Creating reservation at $url');

      final requestBody = {
        'customerName': customerName,
        'phoneNumber': phoneNumber,
        'numberOfGuests': numberOfGuests,
        'reservationTime': reservationTime.toIso8601String(),
        if (specialNotes != null && specialNotes.isNotEmpty)
          'specialNotes': specialNotes,
      };

      debugPrint(
          '📅 TableReservationService: Request body: ${json.encode(requestBody)}');

      final response = await HttpClientService.post(
        url,
        body: json.encode(requestBody),
      );

      debugPrint(
          '📅 TableReservationService: Response status: ${response.statusCode}');
      debugPrint('📅 TableReservationService: Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        debugPrint(
            '✅ TableReservationService: Reservation created successfully');
        return true;
      } else {
        debugPrint(
            '❌ TableReservationService: Failed to create reservation: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ TableReservationService: Error creating reservation: $e');
      return false;
    }
  }

  /// Get all reservations for a specific table
  /// GET {{LAMBDA_HOST}}/table-reservation/tables/:tableId/reservations
  static Future<List<Map<String, dynamic>>?> getTableReservations({
    required String tableId,
  }) async {
    try {
      final url = '$_baseUrl/table-reservation/tables/$tableId/reservations';
      debugPrint('📅 TableReservationService: Fetching reservations from $url');

      final response = await HttpClientService.get(url);
      debugPrint(
          '📅 TableReservationService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('📅 TableReservationService: Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> reservationsJson = responseData['data'];
          final reservations = reservationsJson.cast<Map<String, dynamic>>();
          debugPrint(
              '✅ TableReservationService: Successfully fetched ${reservations.length} reservations');
          return reservations;
        } else {
          debugPrint(
              '📅 TableReservationService: API returned success=false or null data');
          return [];
        }
      } else {
        debugPrint(
            '❌ TableReservationService: Failed to fetch reservations: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ TableReservationService: Error fetching reservations: $e');
      return null;
    }
  }

  /// Get all reservations for today
  /// GET {{LAMBDA_HOST}}/table-reservation/reservations/today
  static Future<List<Map<String, dynamic>>?> getTodaysReservations() async {
    try {
      final url = '$_baseUrl/table-reservation/reservations/today';
      debugPrint(
          '📅 TableReservationService: Fetching today\'s reservations from $url');

      final response = await HttpClientService.get(url);
      debugPrint(
          '📅 TableReservationService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = json.decode(response.body);
        debugPrint('📅 TableReservationService: Response data: $responseData');

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> reservationsJson = responseData['data'];
          final reservations = reservationsJson.cast<Map<String, dynamic>>();
          debugPrint(
              '✅ TableReservationService: Successfully fetched ${reservations.length} today\'s reservations');
          return reservations;
        } else {
          debugPrint(
              '📅 TableReservationService: API returned success=false or null data');
          return [];
        }
      } else {
        debugPrint(
            '❌ TableReservationService: Failed to fetch today\'s reservations: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint(
          '❌ TableReservationService: Error fetching today\'s reservations: $e');
      return null;
    }
  }

  /// Cancel a reservation
  /// DELETE {{LAMBDA_HOST}}/table-reservation/reservations/:reservationId
  static Future<bool> cancelReservation({
    required String reservationId,
  }) async {
    try {
      final url = '$_baseUrl/table-reservation/reservations/$reservationId';
      debugPrint('📅 TableReservationService: Cancelling reservation at $url');

      final response = await HttpClientService.delete(url);
      debugPrint(
          '📅 TableReservationService: Response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        debugPrint(
            '✅ TableReservationService: Reservation cancelled successfully');
        return true;
      } else {
        debugPrint(
            '❌ TableReservationService: Failed to cancel reservation: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ TableReservationService: Error cancelling reservation: $e');
      return false;
    }
  }

  /// Update a reservation
  /// PUT {{LAMBDA_HOST}}/table-reservation/reservations/:reservationId
  static Future<bool> updateReservation({
    required String reservationId,
    String? customerName,
    String? phoneNumber,
    int? numberOfGuests,
    DateTime? reservationTime,
    String? specialNotes,
  }) async {
    try {
      final url = '$_baseUrl/table-reservation/reservations/$reservationId';
      debugPrint('📅 TableReservationService: Updating reservation at $url');

      final requestBody = <String, dynamic>{};

      if (customerName != null) requestBody['customerName'] = customerName;
      if (phoneNumber != null) requestBody['phoneNumber'] = phoneNumber;
      if (numberOfGuests != null)
        requestBody['numberOfGuests'] = numberOfGuests;
      if (reservationTime != null)
        requestBody['reservationTime'] = reservationTime.toIso8601String();
      if (specialNotes != null) requestBody['specialNotes'] = specialNotes;

      debugPrint(
          '📅 TableReservationService: Request body: ${json.encode(requestBody)}');

      final response = await HttpClientService.put(
        url,
        body: json.encode(requestBody),
      );

      debugPrint(
          '📅 TableReservationService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint(
            '✅ TableReservationService: Reservation updated successfully');
        return true;
      } else {
        debugPrint(
            '❌ TableReservationService: Failed to update reservation: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ TableReservationService: Error updating reservation: $e');
      return false;
    }
  }

  /// Check-in a reservation (mark as arrived)
  /// PATCH {{LAMBDA_HOST}}/table-reservation/reservations/:reservationId/checkin
  static Future<bool> checkInReservation({
    required String reservationId,
  }) async {
    try {
      final url =
          '$_baseUrl/table-reservation/reservations/$reservationId/checkin';
      debugPrint('📅 TableReservationService: Checking in reservation at $url');

      final response = await HttpClientService.patch(url);
      debugPrint(
          '📅 TableReservationService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint(
            '✅ TableReservationService: Reservation checked in successfully');
        return true;
      } else {
        debugPrint(
            '❌ TableReservationService: Failed to check in reservation: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint(
          '❌ TableReservationService: Error checking in reservation: $e');
      return false;
    }
  }

  /// Accept a reservation (change status from pending to confirmed)
  /// PUT {{LAMBDA_HOST}}/table-reservation/reservations/:reservationId
  static Future<bool> acceptReservation({
    required String reservationId,
  }) async {
    try {
      final url = '$_baseUrl/table-reservation/reservations/$reservationId';
      debugPrint('📅 TableReservationService: Accepting reservation at $url');

      final requestBody = {
        'status': 'confirmed',
      };

      final response = await HttpClientService.put(url, body: requestBody);
      debugPrint(
          '📅 TableReservationService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint(
            '✅ TableReservationService: Reservation accepted successfully');
        return true;
      } else {
        debugPrint(
            '❌ TableReservationService: Failed to accept reservation: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ TableReservationService: Error accepting reservation: $e');
      return false;
    }
  }

  /// Reject a reservation (change status from pending to cancelled)
  /// PUT {{LAMBDA_HOST}}/table-reservation/reservations/:reservationId
  static Future<bool> rejectReservation({
    required String reservationId,
  }) async {
    try {
      final url = '$_baseUrl/table-reservation/reservations/$reservationId';
      debugPrint('📅 TableReservationService: Rejecting reservation at $url');

      final requestBody = {
        'status': 'cancelled',
      };

      final response = await HttpClientService.put(url, body: requestBody);
      debugPrint(
          '📅 TableReservationService: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint(
            '✅ TableReservationService: Reservation rejected successfully');
        return true;
      } else {
        debugPrint(
            '❌ TableReservationService: Failed to reject reservation: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ TableReservationService: Error rejecting reservation: $e');
      return false;
    }
  }

  /// Get all reservations for a specific date
  /// GET {{LAMBDA_HOST}}/table-reservation/reservations?date=YYYY-MM-DD&includeMonth=true
  static Future<ReservationsApiResponse?> getAllReservations({
    required DateTime date,
    bool includeMonth = true,
  }) async {
    try {
      final dateString =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      final url =
          '$_baseUrl/table-reservation/reservations?date=$dateString&includeMonth=$includeMonth';
      debugPrint(
          '📅 TableReservationService: Fetching all reservations from $url');

      final response = await HttpClientService.get(url);
      debugPrint(
          '📅 TableReservationService: Response status: ${response.statusCode}');
      debugPrint('📅 TableReservationService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final reservationsResponse = ReservationsApiResponse.fromJson(jsonData);
        debugPrint(
            '✅ TableReservationService: Successfully fetched ${reservationsResponse.data.reservationsInCurrentWeek.length} weekly reservations and ${reservationsResponse.data.reservationsInMonth.length} monthly reservations');
        return reservationsResponse;
      } else {
        debugPrint(
            '❌ TableReservationService: Failed to fetch reservations: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ TableReservationService: Error fetching reservations: $e');
      return null;
    }
  }
}
