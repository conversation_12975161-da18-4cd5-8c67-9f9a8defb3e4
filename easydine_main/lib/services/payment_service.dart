import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/env_config.dart';
import '../utils/http_client.dart';

/// Enum for payment methods
enum PaymentMethod {
  cash('CASH'),
  card('CARD'),
  upi('UPI'),
  wallet('WALLET'),
  bankTransfer('BANK_TRANSFER'),
  split('SPLIT');

  const PaymentMethod(this.value);
  final String value;
}

/// Service for handling payment-related API calls
class PaymentService {
  static final String _baseUrl = EnvConfig.apiBaseUrl;

  /// Complete payment for an order
  /// PATCH {{LAMBDA_HOST}}/order/:orderDetailId/complete
  static Future<bool> completePayment({
    required String orderDetailId,
    required PaymentMethod paymentMethod,
  }) async {
    try {
      final url = '$_baseUrl/order/$orderDetailId/complete';
      debugPrint('💳 PaymentService: Completing payment for order $orderDetailId with method ${paymentMethod.value}');

      final requestBody = {
        'paymentMethod': paymentMethod.value,
      };

      debugPrint('💳 PaymentService: Request URL: $url');
      debugPrint('💳 PaymentService: Request body: ${jsonEncode(requestBody)}');

      final response = await HttpClientService.patch(
        url,
        body: jsonEncode(requestBody),
      );

      debugPrint('💳 PaymentService: Response status: ${response.statusCode}');
      debugPrint('💳 PaymentService: Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        debugPrint('✅ PaymentService: Payment completed successfully');
        return true;
      } else {
        debugPrint('❌ PaymentService: Failed to complete payment: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ PaymentService: Error completing payment: $e');
      return false;
    }
  }

  /// Add tip to order bill
  /// PUT {{LAMBDA_HOST}}/order/update-bill/:billId
  static Future<bool> addTip({
    required String billId,
    required double tipAmount,
  }) async {
    try {
      final url = '$_baseUrl/order/update-bill/$billId';
      debugPrint('💰 PaymentService: Adding tip of $tipAmount to bill $billId');

      final requestBody = {
        'tipAmount': tipAmount,
      };

      debugPrint('💰 PaymentService: Request URL: $url');
      debugPrint('💰 PaymentService: Request body: ${jsonEncode(requestBody)}');

      final response = await HttpClientService.put(
        url,
        body: jsonEncode(requestBody),
      );

      debugPrint('💰 PaymentService: Response status: ${response.statusCode}');
      debugPrint('💰 PaymentService: Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        debugPrint('✅ PaymentService: Tip added successfully');
        return true;
      } else {
        debugPrint('❌ PaymentService: Failed to add tip: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ PaymentService: Error adding tip: $e');
      return false;
    }
  }

  /// Get payment method from string value
  static PaymentMethod? getPaymentMethodFromString(String value) {
    try {
      return PaymentMethod.values.firstWhere(
        (method) => method.value == value.toUpperCase(),
      );
    } catch (e) {
      debugPrint('❌ PaymentService: Unknown payment method: $value');
      return null;
    }
  }

  /// Get all available payment methods
  static List<PaymentMethod> getAllPaymentMethods() {
    return PaymentMethod.values;
  }

  /// Get payment method display name
  static String getPaymentMethodDisplayName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.card:
        return 'Card';
      case PaymentMethod.upi:
        return 'UPI';
      case PaymentMethod.wallet:
        return 'Wallet';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.split:
        return 'Split Payment';
    }
  }
}
