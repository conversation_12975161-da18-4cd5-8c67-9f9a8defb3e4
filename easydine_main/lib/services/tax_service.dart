import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/env_config.dart';
import '../models/tax_models.dart';
import '../utils/http_client.dart';

/// Service for handling tax-related API calls
class TaxService extends ChangeNotifier {
  static final TaxService _instance = TaxService._internal();
  factory TaxService() => _instance;
  TaxService._internal();

  static final String _baseUrl = EnvConfig.apiBaseUrl;
  
  List<Tax> _taxes = [];
  bool _isLoading = false;
  String? _error;
  DateTime? _lastFetched;

  /// Get current taxes
  List<Tax> get taxes => _taxes;

  /// Get enabled taxes only
  List<Tax> get enabledTaxes => _taxes.where((tax) => tax.enabled).toList();

  /// Check if currently loading
  bool get isLoading => _isLoading;

  /// Get current error message
  String? get error => _error;

  /// Check if data is stale (older than 5 minutes)
  bool get isDataStale {
    if (_lastFetched == null) return true;
    return DateTime.now().difference(_lastFetched!).inMinutes > 5;
  }

  /// Fetch common taxes from API
  /// GET {{LAMBDA_QA}}/food-tax/common-tax
  Future<bool> fetchCommonTaxes({bool forceRefresh = false}) async {
    // Don't fetch if data is fresh and not forcing refresh
    if (!forceRefresh && !isDataStale && _taxes.isNotEmpty) {
      debugPrint('🏷️ TaxService: Using cached tax data');
      return true;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final url = '$_baseUrl/food-tax/common-tax';
      debugPrint('🏷️ TaxService: Fetching common taxes from $url');

      final response = await HttpClientService.get(url);
      debugPrint('🏷️ TaxService: Response status: ${response.statusCode}');
      debugPrint('🏷️ TaxService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        final taxResponse = TaxApiResponse.fromJson(jsonData);

        if (taxResponse.success) {
          _taxes = taxResponse.data.taxes;
          _lastFetched = DateTime.now();
          _error = null;
          
          debugPrint('✅ TaxService: Successfully fetched ${_taxes.length} taxes');
          debugPrint('🏷️ TaxService: Enabled taxes: ${enabledTaxes.length}');
          
          // Log tax details for debugging
          for (final tax in enabledTaxes) {
            debugPrint('🏷️ TaxService: ${tax.taxCode} - ${tax.taxValue}% (${tax.taxType})');
          }
          
          notifyListeners();
          return true;
        } else {
          _error = taxResponse.message;
          debugPrint('❌ TaxService: API returned error: ${taxResponse.message}');
        }
      } else {
        _error = 'Failed to fetch taxes: HTTP ${response.statusCode}';
        debugPrint('❌ TaxService: HTTP error: ${response.statusCode}');
      }
    } catch (e) {
      _error = 'Error fetching taxes: $e';
      debugPrint('❌ TaxService: Exception: $e');
    }

    _isLoading = false;
    notifyListeners();
    return false;
  }

  /// Calculate total tax amount for a given subtotal
  double calculateTotalTaxAmount(double subtotal) {
    if (subtotal <= 0) return 0.0;
    
    return enabledTaxes.fold(0.0, (total, tax) {
      final taxAmount = tax.calculateTaxAmount(subtotal);
      debugPrint('🏷️ TaxService: ${tax.taxCode} tax on ₹$subtotal = ₹$taxAmount');
      return total + taxAmount;
    });
  }

  /// Get tax breakdown for display
  List<TaxBreakdown> getTaxBreakdown(double subtotal) {
    if (subtotal <= 0) return [];
    
    return enabledTaxes.map((tax) {
      final amount = tax.calculateTaxAmount(subtotal);
      return TaxBreakdown(
        tax: tax,
        amount: amount,
      );
    }).where((breakdown) => breakdown.amount > 0).toList();
  }

  /// Calculate grand total (subtotal + taxes)
  double calculateGrandTotal(double subtotal) {
    final taxAmount = calculateTotalTaxAmount(subtotal);
    return subtotal + taxAmount;
  }

  /// Clear cached data
  void clearCache() {
    _taxes = [];
    _lastFetched = null;
    _error = null;
    notifyListeners();
  }

  /// Initialize tax service (fetch taxes on app start)
  Future<void> initialize() async {
    debugPrint('🏷️ TaxService: Initializing...');
    await fetchCommonTaxes();
  }
}

/// Tax breakdown for display purposes
class TaxBreakdown {
  final Tax tax;
  final double amount;

  const TaxBreakdown({
    required this.tax,
    required this.amount,
  });

  /// Get display text for this tax breakdown
  String get displayText => tax.displayText;

  /// Get formatted amount
  String get formattedAmount => '₹${amount.toStringAsFixed(2)}';
}
