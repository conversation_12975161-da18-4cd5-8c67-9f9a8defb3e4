import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/env_config.dart';
import '../models/cart_models.dart';
import '../utils/http_client.dart';

class CartService {
  static final String _baseUrl = EnvConfig.apiBaseUrl;

  /// Create or get cart for staff
  /// GET {{LAMBDA_HOST}}/cart/staff?orderTypeId={orderTypeId}
  /// orderTypeId is a REQUIRED parameter
  static Future<Cart?> getOrCreateCart({String? orderTypeId}) async {
    try {
      // orderTypeId is compulsory - if not provided, return null
      if (orderTypeId == null || orderTypeId.isEmpty) {
        debugPrint('❌ CartService: orderTypeId is required but not provided');
        return null;
      }

      // Build URL with required orderTypeId query parameter
      String url = '$_baseUrl/cart/staff?orderTypeId=$orderTypeId';
      debugPrint('🛒 CartService: Getting/creating cart with required order type ID: $orderTypeId');
      debugPrint('🛒 CartService: Getting/creating cart at $url');

      final response = await HttpClientService.get(url);
      debugPrint('🛒 CartService: Response status: ${response.statusCode}');
      debugPrint('🛒 CartService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          final cart = Cart.fromJson(responseData['data']);
          debugPrint(
              '🛒 CartService: Parsed cart with ${cart.items.length} items');
          for (final item in cart.items) {
            debugPrint(
                '🛒 CartService: Cart item ${item.id} has quantity ${item.quantity}');
          }
          return cart;
        } else {
          debugPrint('❌ CartService: API returned success=false or no data');
          return null;
        }
      } else {
        debugPrint(
            '❌ CartService: Failed to get cart: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error getting cart: $e');
      return null;
    }
  }

  /// Add item to cart
  /// POST {{LAMBDA_HOST}}/cart/staff/items
  static Future<bool> addItemToCart(AddItemToCartRequest request) async {
    try {
      final url = '$_baseUrl/cart/staff/items';
      debugPrint('🛒 CartService: Adding item to cart at $url');
      debugPrint(
          '🛒 CartService: Request body: ${jsonEncode(request.toJson())}');

      final response = await HttpClientService.post(
        url,
        body: jsonEncode(request.toJson()),
      );

      debugPrint(
          '🛒 CartService: Add item response status: ${response.statusCode}');
      debugPrint('🛒 CartService: Add item response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return responseData['success'] == true;
      } else {
        debugPrint(
            '❌ CartService: Failed to add item: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error adding item to cart: $e');
      return false;
    }
  }

  /// Delete item from cart
  /// DELETE {{LAMBDA_HOST}}/cart/staff/items/{cartItemId}
  static Future<bool> deleteItemFromCart(String cartItemId) async {
    try {
      final url = '$_baseUrl/cart/staff/items/$cartItemId';
      debugPrint('🛒 CartService: Deleting item from cart at $url');

      final response = await HttpClientService.delete(url);
      debugPrint(
          '🛒 CartService: Delete item response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to delete item: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error deleting item from cart: $e');
      return false;
    }
  }

  /// Clear all items from cart
  /// DELETE {{LAMBDA_HOST}}/cart/staff/clear
  static Future<bool> clearCart() async {
    try {
      final url = '$_baseUrl/cart/staff/clear';
      debugPrint('🛒 CartService: Clearing cart at $url');

      final response = await HttpClientService.delete(url);
      debugPrint(
          '🛒 CartService: Clear cart response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to clear cart: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error clearing cart: $e');
      return false;
    }
  }

  /// Set cart to hold status
  /// PATCH {{LAMBDA_HOST}}/cart/staff/hold
  static Future<bool> holdCart() async {
    try {
      final url = '$_baseUrl/cart/staff/hold';
      debugPrint('🛒 CartService: Setting cart to hold at $url');

      final response = await HttpClientService.patch(url);
      debugPrint(
          '🛒 CartService: Hold cart response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to hold cart: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error holding cart: $e');
      return false;
    }
  }

  /// Update item quantity
  /// PUT {{LAMBDA_HOST}}/cart/staff/items/{cartItemId}
  static Future<bool> updateItemQuantity(
      String cartItemId, int quantity) async {
    try {
      final url = '$_baseUrl/cart/staff/items/$cartItemId';
      final request = UpdateItemQuantityRequest(quantity: quantity);

      debugPrint('🛒 CartService: Updating item quantity at $url');
      debugPrint(
          '🛒 CartService: Request body: ${jsonEncode(request.toJson())}');

      final response = await HttpClientService.put(
        url,
        body: jsonEncode(request.toJson()),
      );

      debugPrint(
          '🛒 CartService: Update quantity response status: ${response.statusCode}');
      debugPrint(
          '🛒 CartService: Update quantity response body: ${response.body}');

      if (response.statusCode == 200) {
        debugPrint('✅ CartService: Quantity update successful');
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to update quantity: HTTP ${response.statusCode}');
        debugPrint('❌ CartService: Error response body: ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error updating item quantity: $e');
      return false;
    }
  }

  /// Update item details
  /// PATCH {{LAMBDA_HOST}}/cart/staff/update/{cartItemId}
  static Future<bool> updateItem(
      String cartItemId, Map<String, dynamic> updates) async {
    try {
      final url = '$_baseUrl/cart/staff/update/$cartItemId';
      debugPrint('🛒 CartService: Updating item at $url');
      debugPrint('🛒 CartService: Request body: ${jsonEncode(updates)}');

      final response = await HttpClientService.patch(
        url,
        body: jsonEncode(updates),
      );

      debugPrint(
          '🛒 CartService: Update item response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to update item: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error updating item: $e');
      return false;
    }
  }

  /// Activate cart
  /// PATCH {{LAMBDA_HOST}}/cart/staff/{cartId}/activate
  static Future<bool> activateCart(String cartId) async {
    try {
      final url = '$_baseUrl/cart/staff/$cartId/activate';
      debugPrint('🛒 CartService: Activating cart at $url');

      final response = await HttpClientService.patch(url);
      debugPrint(
          '🛒 CartService: Activate cart response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to activate cart: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error activating cart: $e');
      return false;
    }
  }

  /// Fetch all carts
  /// GET {{LAMBDA_HOST}}/cart/list
  static Future<List<Cart>?> fetchAllCarts() async {
    try {
      final url = '$_baseUrl/cart/list';
      debugPrint('🛒 CartService: Fetching all carts at $url');

      final response = await HttpClientService.get(url);
      debugPrint(
          '🛒 CartService: Fetch all carts response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> cartsJson = responseData['data'];
          return cartsJson.map((json) => Cart.fromJson(json)).toList();
        } else {
          debugPrint('❌ CartService: API returned success=false or no data');
          return [];
        }
      } else {
        debugPrint(
            '❌ CartService: Failed to fetch carts: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error fetching all carts: $e');
      return null;
    }
  }

  /// Confirm cart (place order)
  /// POST {{LAMBDA_HOST}}/cart/staff/confirm
  static Future<bool> confirmCart(ConfirmCartRequest request) async {
    try {
      final url = '$_baseUrl/cart/staff/confirm';
      debugPrint('🛒 CartService: Confirming cart at $url');
      debugPrint(
          '🛒 CartService: Request body: ${jsonEncode(request.toJson())}');

      final response = await HttpClientService.post(
        url,
        body: jsonEncode(request.toJson()),
      );

      debugPrint(
          '🛒 CartService: Confirm cart response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to confirm cart: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error confirming cart: $e');
      return false;
    }
  }

  /// Delete cart
  /// DELETE {{LAMBDA_HOST}}/cart/staff/delete-cart/{cartId}
  static Future<bool> deleteCart(String cartId) async {
    try {
      final url = '$_baseUrl/cart/staff/delete-cart/$cartId';
      debugPrint('🛒 CartService: Deleting cart at $url');

      final response = await HttpClientService.delete(url);
      debugPrint(
          '🛒 CartService: Delete cart response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to delete cart: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error deleting cart: $e');
      return false;
    }
  }

  /// Add or update miscellaneous item to cart
  /// PATCH {{LAMBDA_HOST}}/cart/staff/add-details/{cartId}
  static Future<bool> updateMiscItem(
      String cartId, List<Map<String, dynamic>> miscItems) async {
    try {
      final url = '$_baseUrl/cart/staff/add-details/$cartId';
      debugPrint('🛒 CartService: Updating misc items at $url');
      debugPrint('🛒 CartService: Request body: ${jsonEncode({
            'miscItems': miscItems
          })}');

      final response = await HttpClientService.patch(
        url,
        body: jsonEncode({'miscItems': miscItems}),
      );

      debugPrint(
          '🛒 CartService: Update misc item response status: ${response.statusCode}');
      debugPrint(
          '🛒 CartService: Update misc item response body: ${response.body}');

      if (response.statusCode == 200) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to update misc item: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error updating misc item: $e');
      return false;
    }
  }

  /// Delete miscellaneous item from cart
  /// DELETE {{LAMBDA_HOST}}/cart/staff/delete-misc/{cartId}/{miscItemId}
  static Future<bool> deleteMiscItem(String cartId, String miscItemId) async {
    try {
      final url = '$_baseUrl/cart/staff/delete-misc/$cartId/$miscItemId';
      debugPrint('🛒 CartService: Deleting misc item at $url');

      final response = await HttpClientService.delete(url);
      debugPrint(
          '🛒 CartService: Delete misc item response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to delete misc item: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error deleting misc item: $e');
      return false;
    }
  }

  /// Add or update alert item to cart
  /// PATCH {{LAMBDA_HOST}}/cart/staff/add-details/{cartId}
  static Future<bool> updateAlertItem(
      String cartId, List<Map<String, dynamic>> alerts) async {
    try {
      final url = '$_baseUrl/cart/staff/add-details/$cartId';
      debugPrint('🛒 CartService: Updating alert items at $url');
      debugPrint(
          '🛒 CartService: Request body: ${jsonEncode({'alerts': alerts})}');

      final response = await HttpClientService.patch(
        url,
        body: jsonEncode({'alerts': alerts}),
      );

      debugPrint(
          '🛒 CartService: Update alert item response status: ${response.statusCode}');
      debugPrint(
          '🛒 CartService: Update alert item response body: ${response.body}');

      if (response.statusCode == 200) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to update alert item: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error updating alert item: $e');
      return false;
    }
  }

  /// Delete alert item from cart
  /// DELETE {{LAMBDA_HOST}}/cart/staff/delete-alert/{cartId}/{alertId}
  static Future<bool> deleteAlertItem(String cartId, String alertId) async {
    try {
      final url = '$_baseUrl/cart/staff/delete-alert/$cartId/$alertId';
      debugPrint('🛒 CartService: Deleting alert item at $url');

      final response = await HttpClientService.delete(url);
      debugPrint(
          '🛒 CartService: Delete alert item response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to delete alert item: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error deleting alert item: $e');
      return false;
    }
  }

  /// Update cart note
  /// PATCH {{LAMBDA_HOST}}/cart/staff/add-details/{cartId}
  static Future<bool> updateNote(String cartId, String note) async {
    try {
      final url = '$_baseUrl/cart/staff/add-details/$cartId';
      debugPrint('🛒 CartService: Updating note at $url');
      debugPrint('🛒 CartService: Request body: ${jsonEncode({'note': note})}');

      final response = await HttpClientService.patch(
        url,
        body: jsonEncode({'note': note}),
      );

      debugPrint(
          '🛒 CartService: Update note response status: ${response.statusCode}');
      debugPrint('🛒 CartService: Update note response body: ${response.body}');

      if (response.statusCode == 200) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to update note: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error updating note: $e');
      return false;
    }
  }

  /// Add cart details (notes, miscItems, alerts, customerId, tableId, orderTypeId)
  /// PATCH {{LAMBDA_HOST}}/cart/staff/add-details/{cartId}
  ///
  /// Expected format for miscItems: [{"name": "Item Name", "price": 10.0}]
  /// Expected format for alerts: [{"note": "Alert message"}]
  static Future<bool> addCartDetails({
    required String cartId,
    String? note,
    List<Map<String, dynamic>>? miscItems,
    List<Map<String, dynamic>>? alerts,
    String? customerId,
    String? tableId,
    String? orderTypeId,
  }) async {
    try {
      final url = '$_baseUrl/cart/staff/add-details/$cartId';

      final Map<String, dynamic> requestBody = {};
      if (note != null) requestBody['note'] = note;

      // Ensure miscItems have the correct format (name and price only)
      if (miscItems != null) {
        requestBody['miscItems'] = miscItems.map((item) => {
          'name': item['name'],
          'price': item['price'],
        }).toList();
      }

      // Ensure alerts have the correct format (note only)
      if (alerts != null) {
        requestBody['alerts'] = alerts.map((alert) => {
          'note': alert['note'],
        }).toList();
      }

      if (customerId != null) requestBody['customerId'] = customerId;
      if (tableId != null) requestBody['tableId'] = tableId;
      if (orderTypeId != null) requestBody['orderTypeId'] = orderTypeId;

      debugPrint('🛒 CartService: Adding cart details at $url');
      debugPrint('🛒 CartService: Request body: ${jsonEncode(requestBody)}');

      final response = await HttpClientService.patch(
        url,
        body: jsonEncode(requestBody),
      );

      debugPrint(
          '🛒 CartService: Add cart details response status: ${response.statusCode}');
      debugPrint('🛒 CartService: Add cart details response body: ${response.body}');

      if (response.statusCode == 200) {
        return true;
      } else {
        debugPrint(
            '❌ CartService: Failed to add cart details: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ CartService: Error adding cart details: $e');
      return false;
    }
  }
}
