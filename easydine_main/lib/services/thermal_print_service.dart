import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/services.dart';
import '../models/order_model.dart';
import '../utils/currency_formatter.dart';

/// Thermal printer bill data model
class ThermalBillData {
  final String restaurantName;
  final String address;
  final String phone;
  final String billNumber;
  final String orderCode;
  final DateTime date;
  final String? tableName;
  final String orderType;
  final List<ThermalBillItem> items;
  final String subtotal;
  final String tipAmount;
  final String totalTax;
  final String totalDiscount;
  final String serviceCharge;
  final String deliveryCharge;
  final String packagingCharge;
  final String totalAmount;
  final String status;
  final String? customerName;
  final String? serverName;
  final String? notes;

  ThermalBillData({
    required this.restaurantName,
    required this.address,
    required this.phone,
    required this.billNumber,
    required this.orderCode,
    required this.date,
    this.tableName,
    required this.orderType,
    required this.items,
    required this.subtotal,
    required this.tipAmount,
    required this.totalTax,
    required this.totalDiscount,
    required this.serviceCharge,
    required this.deliveryCharge,
    required this.packagingCharge,
    required this.totalAmount,
    required this.status,
    this.customerName,
    this.serverName,
    this.notes,
  });

  /// Convert OrderDetail and Bill to ThermalBillData
  factory ThermalBillData.fromOrderAndBill(OrderDetail order, Bill bill) {
    // Combine regular order items and misc items
    List<ThermalBillItem> allItems = [];

    // Add regular order items
    allItems.addAll(order.orderItems.map((item) => ThermalBillItem.fromOrderItem(item)).toList());

    // Add misc items
    if (order.miscItems.isNotEmpty) {
      allItems.addAll(order.miscItems.map((miscItem) {
        final miscItemMap = miscItem as Map<String, dynamic>;
        return ThermalBillItem.fromMiscItem(miscItemMap);
      }).toList());
    }

    return ThermalBillData(
      restaurantName: "EasyDine Restaurant", // You can make this configurable
      address: "123 Main Street, City, State 12345",
      phone: "+****************",
      billNumber: bill.billNumber,
      orderCode: order.orderCode,
      date: bill.createdAt,
      tableName: order.table?.name,
      orderType: order.orderType.name,
      items: allItems,
      subtotal: bill.subtotal,
      tipAmount: bill.tipAmount,
      totalTax: bill.totalTax,
      totalDiscount: bill.totalDiscount,
      serviceCharge: bill.serviceCharge,
      deliveryCharge: bill.deliveryCharge,
      packagingCharge: bill.packagingCharge,
      totalAmount: bill.totalAmount,
      status: bill.status,
      customerName: bill.customerInfo?.name,
      serverName: order.assignedWaiter?.name,
      notes: bill.notes,
    );
  }
}

/// Thermal printer bill item model
class ThermalBillItem {
  final String name;
  final int quantity;
  final String price;
  final List<String>? customizations;

  ThermalBillItem({
    required this.name,
    required this.quantity,
    required this.price,
    this.customizations,
  });

  /// Convert OrderItem to ThermalBillItem
  factory ThermalBillItem.fromOrderItem(OrderItem orderItem) {
    List<String> customizations = [];

    // Add customizations
    if (orderItem.spiciness != null) {
      customizations.add("Spiciness: ${orderItem.spiciness}");
    }

    if (orderItem.dishAddons != null && orderItem.dishAddons!.isNotEmpty) {
      for (var addon in orderItem.dishAddons!) {
        customizations.add("+ ${addon.name} (${addon.quantity}x)");
      }
    }

    if (orderItem.dishExtras != null && orderItem.dishExtras!.isNotEmpty) {
      for (var extra in orderItem.dishExtras!) {
        customizations.add("+ ${extra.name} (${extra.quantity}x)");
      }
    }

    if (orderItem.allergies != null && orderItem.allergies!.isNotEmpty) {
      customizations.add("Allergies: ${orderItem.allergies!.map((a) => a.name).join(', ')}");
    }

    if (orderItem.notes != null && orderItem.notes!.isNotEmpty) {
      customizations.add("Notes: ${orderItem.notes}");
    }

    return ThermalBillItem(
      name: orderItem.name,
      quantity: orderItem.quantity,
      price: orderItem.price,
      customizations: customizations.isNotEmpty ? customizations : null,
    );
  }

  /// Convert misc item to ThermalBillItem
  factory ThermalBillItem.fromMiscItem(Map<String, dynamic> miscItem) {
    final name = miscItem['name'] ?? 'Unknown Item';
    final price = (miscItem['price'] ?? 0).toDouble();

    return ThermalBillItem(
      name: name,
      quantity: 1, // Misc items always have quantity 1
      price: price.toStringAsFixed(2),
      customizations: ['Additional Item'], // Mark as additional item
    );
  }
}

/// Thermal printer service for small POS thermal printers
class ThermalPrintService {
  static const double thermalWidth = 58.0; // 58mm thermal paper width in mm
  static const double thermalHeight = double.infinity; // Variable height
  
  /// Print bill to thermal printer
  Future<void> printBill(ThermalBillData billData) async {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) => generateThermalBillPdf(billData),
      name: 'Bill_${billData.billNumber}.pdf',
      format: PdfPageFormat(
        thermalWidth * PdfPageFormat.mm,
        thermalHeight,
        marginAll: 2 * PdfPageFormat.mm,
      ),
    );
  }

  /// Save bill as PDF
  Future<void> saveBill(ThermalBillData billData) async {
    final pdf = await generateThermalBillPdf(billData);
    final directory = await getApplicationDocumentsDirectory();
    final file = File(
        '${directory.path}/bill_${billData.billNumber}_${DateTime.now().millisecondsSinceEpoch}.pdf');
    await file.writeAsBytes(pdf);
  }

  /// Share bill PDF
  Future<void> shareAndSaveBill(ThermalBillData billData) async {
    final pdf = await generateThermalBillPdf(billData);
    await Printing.sharePdf(
      bytes: pdf,
      filename: 'bill_${billData.billNumber}.pdf',
    );
  }

  /// Generate thermal printer formatted PDF
  Future<Uint8List> generateThermalBillPdf(ThermalBillData billData) async {
    final pdf = pw.Document();
    
    // Load fonts
    final regularFont = await rootBundle.load("assets/fonts/Poppins-Regular.ttf");
    final boldFont = await rootBundle.load("assets/fonts/Poppins-Bold.ttf");
    final poppins = pw.Font.ttf(regularFont);
    final poppinsBold = pw.Font.ttf(boldFont);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat(
          thermalWidth * PdfPageFormat.mm,
          thermalHeight,
          marginAll: 2 * PdfPageFormat.mm,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              // Restaurant header
              _buildThermalHeader(billData, poppinsBold, poppins),
              
              pw.SizedBox(height: 8),
              _buildDivider(),
              pw.SizedBox(height: 8),
              
              // Order info
              _buildOrderInfo(billData, poppins, poppinsBold),
              
              pw.SizedBox(height: 8),
              _buildDivider(),
              pw.SizedBox(height: 8),
              
              // Items
              _buildItemsList(billData, poppins, poppinsBold),
              
              pw.SizedBox(height: 8),
              _buildDivider(),
              pw.SizedBox(height: 8),
              
              // Bill summary
              _buildBillSummary(billData, poppins, poppinsBold),
              
              pw.SizedBox(height: 8),
              _buildDivider(),
              pw.SizedBox(height: 8),
              
              // Footer
              _buildThermalFooter(billData, poppins),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// Build thermal printer header
  pw.Widget _buildThermalHeader(ThermalBillData billData, pw.Font boldFont, pw.Font regularFont) {
    return pw.Column(
      children: [
        pw.Text(
          billData.restaurantName,
          style: pw.TextStyle(
            font: boldFont,
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
          ),
          textAlign: pw.TextAlign.center,
        ),
        pw.SizedBox(height: 2),
        pw.Text(
          billData.address,
          style: pw.TextStyle(font: regularFont, fontSize: 8),
          textAlign: pw.TextAlign.center,
        ),
        pw.Text(
          billData.phone,
          style: pw.TextStyle(font: regularFont, fontSize: 8),
          textAlign: pw.TextAlign.center,
        ),
      ],
    );
  }

  /// Build order information section
  pw.Widget _buildOrderInfo(ThermalBillData billData, pw.Font regularFont, pw.Font boldFont) {
    final dateStr = "${billData.date.day}/${billData.date.month}/${billData.date.year} ${billData.date.hour}:${billData.date.minute.toString().padLeft(2, '0')}";
    
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        _buildInfoRow("Bill #:", billData.billNumber, regularFont, boldFont),
        _buildInfoRow("Order #:", billData.orderCode, regularFont, boldFont),
        _buildInfoRow("Date:", dateStr, regularFont, boldFont),
        if (billData.tableName != null)
          _buildInfoRow("Table:", billData.tableName!, regularFont, boldFont),
        _buildInfoRow("Type:", billData.orderType, regularFont, boldFont),
        if (billData.serverName != null)
          _buildInfoRow("Server:", billData.serverName!, regularFont, boldFont),
        if (billData.customerName != null)
          _buildInfoRow("Customer:", billData.customerName!, regularFont, boldFont),
      ],
    );
  }

  /// Build info row helper
  pw.Widget _buildInfoRow(String label, String value, pw.Font regularFont, pw.Font boldFont) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(label, style: pw.TextStyle(font: regularFont, fontSize: 8)),
        pw.Text(value, style: pw.TextStyle(font: boldFont, fontSize: 8)),
      ],
    );
  }

  /// Build items list
  pw.Widget _buildItemsList(ThermalBillData billData, pw.Font regularFont, pw.Font boldFont) {
    return pw.Column(
      children: billData.items.map((item) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Item name and price
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Expanded(
                  child: pw.Text(
                    "${item.quantity}x ${item.name}",
                    style: pw.TextStyle(font: boldFont, fontSize: 9),
                  ),
                ),
                pw.Text(
                  CurrencyFormatter.format(double.tryParse(item.price) ?? 0.0),
                  style: pw.TextStyle(font: boldFont, fontSize: 9),
                ),
              ],
            ),
            // Customizations
            if (item.customizations != null && item.customizations!.isNotEmpty)
              ...item.customizations!.map((customization) => 
                pw.Padding(
                  padding: pw.EdgeInsets.only(left: 8, top: 1),
                  child: pw.Text(
                    customization,
                    style: pw.TextStyle(font: regularFont, fontSize: 7),
                  ),
                ),
              ),
            pw.SizedBox(height: 4),
          ],
        );
      }).toList(),
    );
  }

  /// Build bill summary
  pw.Widget _buildBillSummary(ThermalBillData billData, pw.Font regularFont, pw.Font boldFont) {
    return pw.Column(
      children: [
        _buildSummaryRow("Subtotal:", "\$${billData.subtotal}", regularFont),
        if (double.parse(billData.totalDiscount) > 0)
          _buildSummaryRow("Discount:", "-\$${billData.totalDiscount}", regularFont),
        if (double.parse(billData.totalTax) > 0)
          _buildSummaryRow("Tax:", "\$${billData.totalTax}", regularFont),
        if (double.parse(billData.serviceCharge) > 0)
          _buildSummaryRow("Service Charge:", "\$${billData.serviceCharge}", regularFont),
        if (double.parse(billData.deliveryCharge) > 0)
          _buildSummaryRow("Delivery:", "\$${billData.deliveryCharge}", regularFont),
        if (double.parse(billData.packagingCharge) > 0)
          _buildSummaryRow("Packaging:", "\$${billData.packagingCharge}", regularFont),
        if (double.parse(billData.tipAmount) > 0)
          _buildSummaryRow("Tip:", "\$${billData.tipAmount}", regularFont),
        pw.SizedBox(height: 4),
        _buildSummaryRow("TOTAL:", "\$${billData.totalAmount}", boldFont, isTotal: true),
      ],
    );
  }

  /// Build summary row helper
  pw.Widget _buildSummaryRow(String label, String value, pw.Font font, {bool isTotal = false}) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            font: font,
            fontSize: isTotal ? 10 : 8,
            fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
          ),
        ),
        pw.Text(
          value,
          style: pw.TextStyle(
            font: font,
            fontSize: isTotal ? 10 : 8,
            fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
          ),
        ),
      ],
    );
  }

  /// Build thermal printer footer
  pw.Widget _buildThermalFooter(ThermalBillData billData, pw.Font regularFont) {
    return pw.Column(
      children: [
        if (billData.notes != null && billData.notes!.isNotEmpty) ...[
          pw.Text(
            "Notes: ${billData.notes}",
            style: pw.TextStyle(font: regularFont, fontSize: 7),
            textAlign: pw.TextAlign.center,
          ),
          pw.SizedBox(height: 4),
        ],
        pw.Text(
          "Status: ${billData.status}",
          style: pw.TextStyle(font: regularFont, fontSize: 8),
          textAlign: pw.TextAlign.center,
        ),
        pw.SizedBox(height: 8),
        pw.Text(
          "Thank you for dining with us!",
          style: pw.TextStyle(font: regularFont, fontSize: 8),
          textAlign: pw.TextAlign.center,
        ),
        pw.Text(
          "Please come again",
          style: pw.TextStyle(font: regularFont, fontSize: 7),
          textAlign: pw.TextAlign.center,
        ),
      ],
    );
  }

  /// Build divider line
  pw.Widget _buildDivider() {
    return pw.Container(
      width: double.infinity,
      height: 0.5,
      color: PdfColors.black,
    );
  }
}
