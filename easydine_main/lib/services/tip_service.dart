import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../config/env_config.dart';
import '../utils/http_client.dart';

/// Service for handling tip-related API calls
class TipService {
  static final String _baseUrl = EnvConfig.apiBaseUrl;

  /// Add tip to order bill
  /// PUT {{LAMBDA_HOST}}/order/update-bill/:billId
  static Future<bool> addTip({
    required String billId,
    required double tipAmount,
  }) async {
    try {
      final url = '$_baseUrl/order/update-bill/$billId';
      debugPrint('💰 TipService: Adding tip of $tipAmount to bill $billId');

      final requestBody = {
        'tipAmount': tipAmount,
      };

      debugPrint('💰 TipService: Request URL: $url');
      debugPrint('💰 TipService: Request body: ${jsonEncode(requestBody)}');

      final response = await HttpClientService.put(
        url,
        body: jsonEncode(requestBody),
      );

      debugPrint('💰 TipService: Response status: ${response.statusCode}');
      debugPrint('💰 TipService: Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        debugPrint('✅ TipService: Tip added successfully');
        return true;
      } else {
        debugPrint('❌ TipService: Failed to add tip: HTTP ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ TipService: Error adding tip: $e');
      return false;
    }
  }

  /// Validate tip amount
  static bool isValidTipAmount(double tipAmount) {
    return tipAmount > 0 && tipAmount <= 10000; // Reasonable upper limit
  }

  /// Format tip amount for display
  static String formatTipAmount(double tipAmount) {
    return '₹${tipAmount.toStringAsFixed(2)}';
  }

  /// Calculate tip percentage
  static double calculateTipPercentage(double tipAmount, double billTotal) {
    if (billTotal <= 0) return 0;
    return (tipAmount / billTotal) * 100;
  }

  /// Calculate tip amount from percentage
  static double calculateTipFromPercentage(double percentage, double billTotal) {
    return (percentage / 100) * billTotal;
  }

  /// Get suggested tip amounts based on bill total
  static List<double> getSuggestedTipAmounts(double billTotal) {
    if (billTotal <= 0) return [];
    
    return [
      calculateTipFromPercentage(10, billTotal), // 10%
      calculateTipFromPercentage(15, billTotal), // 15%
      calculateTipFromPercentage(20, billTotal), // 20%
    ];
  }

  /// Get suggested tip percentages
  static List<int> getSuggestedTipPercentages() {
    return [10, 15, 20];
  }
}
