# Bill Modification Implementation

## Overview

This implementation adds support for tip and discount management in the EasyDine Flutter app, including manager PIN verification for discounts and integration with the cart confirmation API.

## Key Features

1. **Manager PIN Verification**: 5-digit PIN verification using the API endpoint `{{HOSTNAME}}:{{PORT}}/staff-manage/verify-manager-pin`
2. **Tip and Discount Management**: State management for tip and discount amounts
3. **Cart Confirmation Integration**: Tip and discount amounts are automatically included in the `confirmcart` API call

## Implementation Details

### 1. Manager Service (`lib/services/manager_service.dart`)

- **API Endpoint**: `{{HOSTNAME}}:{{PORT}}/staff-manage/verify-manager-pin`
- **PIN Format**: Exactly 5 digits (as specified in requirements)
- **Request Body**: `{"pin": "71820"}` (example)
- **Validation**: Ensures PIN is exactly 5 digits before API call

### 2. Bill Modification State Management

#### BillModificationState (`lib/models/bill_modification_state.dart`)
- Immutable state class using Equatable
- Tracks tip amount, discount amount, redeem code, and manager approval status
- Provides validation methods for submission readiness

#### BillModificationService (`lib/services/bill_modification_service.dart`)
- Singleton service extending ChangeNotifier
- Manages bill modification state across the app
- Provides methods to update tip, discount, and approval status
- Automatically resets manager approval when discount changes

### 3. Updated Cart Models

#### ConfirmCartRequest (`lib/models/cart_models.dart`)
- Added `tipAmount` and `discountAmount` fields
- Both fields are optional (nullable)
- Automatically included in JSON serialization when values > 0

### 4. Updated Dialogs

#### ModifyBillDialog (`lib/dialogs/modifyBillDialog.dart`)
- Updated to use 5-digit PIN input (changed from 4-digit)
- Integrated with ManagerService for API-based PIN verification
- Uses BillModificationService to persist state
- Initializes form fields with existing values from service

### 5. Cart Confirmation Integration

#### POSBloc (`lib/blocs/pos/pos_bloc.dart`)
- Updated `_onPlaceOrder` method to include tip and discount amounts
- Retrieves values from BillModificationService before creating ConfirmCartRequest
- Automatically resets bill modifications after successful order placement
- Includes debug logging for tip and discount amounts

## API Integration

### Manager PIN Verification
```http
POST {{HOSTNAME}}:{{PORT}}/staff-manage/verify-manager-pin
Content-Type: application/json

{
  "pin": "71820"
}
```

### Cart Confirmation with Tip and Discount
```http
POST {{LAMBDA_HOST}}/cart/staff/confirm
Content-Type: application/json

{
  "assignedWaiterId": "63ca6f80-0f1e-40ea-bf6a-96bd807d18d4",
  "tableId": "32893947-ca3e-49ed-be55-fe09aae60fed",
  "orderTypeId": "51e9bfd5-869d-4f93-865f-3a2c72ce5ae0",
  "tipAmount": 20,
  "discountAmount": 10
}
```

## Usage Flow

1. **User clicks "Bill" option** in extra options row
2. **ModifyBillDialog opens** with current tip/discount values
3. **User enters tip and/or discount amounts**
4. **If discount > 0**: Manager PIN verification dialog appears
5. **Manager enters 5-digit PIN**: API call to verify PIN
6. **On successful verification**: Bill modifications are saved to service
7. **When "Place Order" is clicked**: Tip and discount are included in confirmcart API
8. **After successful order**: Bill modifications are automatically reset

## State Management

The implementation uses a simple service-based state management approach:

- `BillModificationService` acts as a singleton state manager
- Uses `ChangeNotifier` for reactive updates
- State persists across dialog opens/closes until order is placed
- Automatic validation ensures discount requires manager approval

## Error Handling

- PIN validation (5-digit format) before API call
- Fallback to default PIN (71820) if API fails
- Clear error messages for invalid PIN attempts
- Graceful handling of network errors

## Testing

A display widget (`BillModificationDisplay`) is provided to show current bill modification state for testing purposes. This can be added to any screen to verify the state management is working correctly.

## Configuration

The implementation uses the existing environment configuration:
- Base URL from `EnvConfig.apiBaseUrl`
- Standard HTTP client with interceptors
- Consistent error handling and logging patterns
