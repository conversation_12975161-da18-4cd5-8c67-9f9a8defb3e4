import 'package:easydine_main/models/menuItem.dart';
import 'package:equatable/equatable.dart';

abstract class POSEvent extends Equatable {
  const POSEvent();

  @override
  List<Object?> get props => [];
}

class CategorySelected extends POSEvent {
  final String category;
  const CategorySelected(this.category);

  @override
  List<Object> get props => [category];
}

class AddToCart extends POSEvent {
  final String id;
  final String name;
  final double price;
  final Map<String, dynamic>? customization;
  final bool syncWithServer;

  const AddToCart(
    MenuItem menuItem, {
    required this.id,
    required this.name,
    required this.price,
    this.customization,
    this.syncWithServer = true,
  });

  @override
  List<Object?> get props => [id, name, price, customization, syncWithServer];
}

class AddCustomizedItemToCart extends POSEvent {
  final String cartItemId;
  final Map<String, dynamic> customizationData;

  const AddCustomizedItemToCart({
    required this.cartItemId,
    required this.customizationData,
  });

  @override
  List<Object> get props => [cartItemId, customizationData];
}

class UpdateCartItemQuantity extends POSEvent {
  final String id;
  final int quantity;

  const UpdateCartItemQuantity({
    required this.id,
    required this.quantity,
  });

  @override
  List<Object> get props => [id, quantity];
}

class RemoveFromCart extends POSEvent {
  final String id;
  const RemoveFromCart(this.id);

  @override
  List<Object> get props => [id];
}

class ClearCart extends POSEvent {}

class PlaceOrder extends POSEvent {
  final String orderId;
  final int priority;
  final String orderType;
  final String tableNumber;

  const PlaceOrder({
    required this.orderId,
    required this.priority,
    required this.orderType,
    required this.tableNumber,
  });

  @override
  List<Object> get props => [orderId, priority, orderType, tableNumber];
}

class AddItemsToOrder extends POSEvent {
  final String orderDetailId;

  const AddItemsToOrder({
    required this.orderDetailId,
  });

  @override
  List<Object> get props => [orderDetailId];
}

class LengthOfCartItems extends POSEvent {
  final int length;

  const LengthOfCartItems(this.length);

  @override
  List<Object?> get props => [length];
}

class UpdateOrderPriority extends POSEvent {
  final int priority;

  const UpdateOrderPriority({required this.priority});

  @override
  List<Object> get props => [priority];
}

class SearchMenuItems extends POSEvent {
  final String query;

  const SearchMenuItems(this.query);
}

class SyncWithServerCart extends POSEvent {}

class LoadMenuItems extends POSEvent {
  final String? branchId;
  const LoadMenuItems({this.branchId});

  @override
  List<Object?> get props => [branchId];
}

class AddToServerCart extends POSEvent {
  final String dishId;
  final int quantity;
  final String type;
  final String? notes;
  final List<String> allergyIds;

  const AddToServerCart({
    required this.dishId,
    this.quantity = 1,
    this.type = "customized",
    this.notes,
    this.allergyIds = const [],
  });

  @override
  List<Object?> get props => [dishId, quantity, type, notes, allergyIds];
}

class RemoveFromServerCart extends POSEvent {
  final String cartItemId; // Changed from dishId to cartItemId

  const RemoveFromServerCart({required this.cartItemId});

  @override
  List<Object> get props => [cartItemId];
}

class ClearServerCart extends POSEvent {}

class SyncCartWithServer extends POSEvent {
  final bool forceRefresh;

  const SyncCartWithServer({this.forceRefresh = false});

  @override
  List<Object> get props => [forceRefresh];
}

class InitializePOS extends POSEvent {
  const InitializePOS();
}

class LoadExistingCart extends POSEvent {
  const LoadExistingCart();
}
