import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/menuItem.dart';
import '../../models/customization_models.dart' as custom_models;
import '../../models/cartItem.dart' as cart_models;
import '../../services/customization_service.dart';
import '../../services/cart_service.dart';
import '../../services/order_type_preference_service.dart';
import '../pos/pos_bloc.dart';
import '../pos/pos_event.dart';
import '../../utils/currency_formatter.dart';

class ItemCustomizationBottomSheet extends StatefulWidget {
  final MenuItem item;
  final String? cartItemId; // Optional cart item ID for updating existing items

  const ItemCustomizationBottomSheet({
    super.key,
    required this.item,
    this.cartItemId,
  });

  @override
  State<ItemCustomizationBottomSheet> createState() =>
      _ItemCustomizationBottomSheetState();
}

class _ItemCustomizationBottomSheetState
    extends State<ItemCustomizationBottomSheet> {
  // Loading state
  bool isLoading = true;
  String? errorMessage;

  // API Data
  List<custom_models.DishAddon>? dishAddons;
  List<custom_models.DishExtra>? dishExtras;
  List<custom_models.Allergy>? allergies;
  List<custom_models.OrderType>? orderTypes;

  // Selected customizations
  List<custom_models.DishAddon> selectedAddons = [];
  List<custom_models.DishExtra> selectedExtras = [];
  List<custom_models.Allergy> selectedAllergies = [];
  custom_models.OrderType? selectedOrderType;

  // Cooking Options
  final List<String> cookingOptions = [
    'Well Done',
    'Medium',
    'Rare',
    'No Preference'
  ];
  String? selectedCookingOption;

  // Additional Notes
  final TextEditingController notesController = TextEditingController();

  // Theme colors
  final Color bgColor = Color(0xFF121212);
  final Color surfaceColor = Color(0xFF1E1E1E);
  final Color primaryColor = Color(0xFF4CAF50);
  final Color accentColor = Color(0xFF8BC34A);
  final Color textColor = Color(0xFFE0E0E0);
  final Color secondaryTextColor = Color(0xFF9E9E9E);
  final Color cardColor = Color(0xFF252525);
  final Color dividerColor = Color(0xFF353535);

  @override
  void initState() {
    super.initState();
    _loadCustomizationData();
  }

  /// Add customization to existing cart item (no new item creation)
  Future<void> _addCustomizedItemToCart(
      Map<String, dynamic> orderDetails) async {
    try {
      // If we have a specific cart item ID, use it directly
      if (widget.cartItemId != null) {
        debugPrint(
            '🛒 CustomizationModal: Updating existing cart item: ${widget.cartItemId}');
        await _updateCartItemWithCustomization(
            widget.cartItemId!, orderDetails);
        return;
      }

      // Otherwise, find the cart item by dish ID (fallback for existing behavior)
      // Ensure we have a valid order type ID (required for cart operations)
      final orderTypeId = await OrderTypePreferenceService.ensureOrderTypeId();
      final currentCart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);
      if (currentCart == null || currentCart.items.isEmpty) {
        debugPrint(
            '❌ CustomizationModal: No cart found for customization update');
        return;
      }

      // Find the most recently added item with matching dish ID
      final targetItem = currentCart.items
          .where((item) => item.dishId == widget.item.id)
          .lastOrNull;

      if (targetItem == null) {
        debugPrint(
            '❌ CustomizationModal: Could not find cart item to customize');
        return;
      }

      debugPrint(
          '🛒 CustomizationModal: Found cart item to customize: ${targetItem.id}');
      await _updateCartItemWithCustomization(targetItem.id, orderDetails);
    } catch (e) {
      debugPrint('❌ CustomizationModal: Error adding customized item: $e');
    }
  }

  /// Update cart item with customization data
  Future<void> _updateCartItemWithCustomization(
      String cartItemId, Map<String, dynamic> orderDetails) async {
    try {
      // Extract backend data from customization
      final backendData = orderDetails['backendData'] as Map<String, dynamic>?;
      if (backendData == null) {
        debugPrint('❌ CustomizationModal: No backend customization data found');
        return;
      }

      // Prepare customization update request in the exact format expected
      final customizationUpdate = <String, dynamic>{};

      // Add notes if present
      if (backendData['notes'] != null &&
          backendData['notes'].toString().isNotEmpty) {
        customizationUpdate['notes'] = backendData['notes'];
      }

      // Add allergy IDs if present
      if (backendData['allergyIds'] != null) {
        final allergyIds = List<String>.from(backendData['allergyIds']);
        if (allergyIds.isNotEmpty) {
          customizationUpdate['allergyIds'] = allergyIds;
        }
      }

      // Add dish addons if present
      if (backendData['dishAddons'] != null) {
        final dishAddons =
            List<Map<String, dynamic>>.from(backendData['dishAddons']);
        if (dishAddons.isNotEmpty) {
          customizationUpdate['dishAddons'] = dishAddons;
        }
      }

      // Add dish extras if present
      if (backendData['dishExtras'] != null) {
        final dishExtras =
            List<Map<String, dynamic>>.from(backendData['dishExtras']);
        if (dishExtras.isNotEmpty) {
          customizationUpdate['dishExtras'] = dishExtras;
        }
      }

      // Add dish sides if present
      if (backendData['dishSides'] != null) {
        final dishSides =
            List<Map<String, dynamic>>.from(backendData['dishSides']);
        if (dishSides.isNotEmpty) {
          customizationUpdate['dishSides'] = dishSides;
        }
      }

      // Add dish beverages if present
      if (backendData['dishBeverages'] != null) {
        final dishBeverages =
            List<Map<String, dynamic>>.from(backendData['dishBeverages']);
        if (dishBeverages.isNotEmpty) {
          customizationUpdate['dishBeverages'] = dishBeverages;
        }
      }

      // Add dish desserts if present
      if (backendData['dishDesserts'] != null) {
        final dishDesserts =
            List<Map<String, dynamic>>.from(backendData['dishDesserts']);
        if (dishDesserts.isNotEmpty) {
          customizationUpdate['dishDesserts'] = dishDesserts;
        }
      }

      debugPrint(
          '🛒 CustomizationModal: Sending PATCH request to update cart item $cartItemId');
      debugPrint(
          '🛒 CustomizationModal: Endpoint: {{LAMBDA_HOST}}/cart/staff/update/$cartItemId');
      debugPrint(
          '🛒 CustomizationModal: Payload: ${jsonEncode(customizationUpdate)}');

      // Use the new AddCustomizedItemToCart event
      if (mounted) {
        context.read<POSBloc>().add(AddCustomizedItemToCart(
              cartItemId: cartItemId,
              customizationData: customizationUpdate,
            ));
      }
    } catch (e) {
      debugPrint(
          '❌ CustomizationModal: Error updating cart item with customization: $e');
    }
  }

  Future<void> _loadCustomizationData() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final data = await CustomizationService.getAllCustomizationData();
      if (data != null) {
        setState(() {
          dishAddons = data['dishAddons'];
          dishExtras = data['dishExtras'];
          allergies = data['allergies'];
          orderTypes = data['orderTypes'];
          isLoading = false;
        });
      } else {
        setState(() {
          errorMessage = 'Failed to load customization data';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = 'Error: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Container(
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDragHandle(),
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(color: primaryColor),
                    const SizedBox(height: 16),
                    Text(
                      'Loading customization options...',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: textColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (errorMessage != null) {
      return Container(
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDragHandle(),
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                    const SizedBox(height: 16),
                    Text(
                      errorMessage!,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: textColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadCustomizationData,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                        foregroundColor: Colors.white,
                      ),
                      child: Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    final addonsTotal =
        CustomizationService.calculateAddonsTotal(selectedAddons);
    final extrasTotal =
        CustomizationService.calculateExtrasTotal(selectedExtras);
    final totalPrice = widget.item.price + addonsTotal + extrasTotal;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    return Container(
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.5),
            blurRadius: 15,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildDragHandle(),
          Expanded(
            child: isLandscape
                ? _buildLandscapeLayout(totalPrice)
                : _buildPortraitLayout(totalPrice),
          ),
        ],
      ),
    );
  }

  Widget _buildLandscapeLayout(double totalPrice) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left side - Image and Description
        Expanded(
          flex: 3,
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(child: _buildHeader()),
              SliverToBoxAdapter(child: const SizedBox(height: 16)),
              SliverToBoxAdapter(child: _buildDescription()),
            ],
          ),
        ),
        // Vertical Divider
        Container(
          width: 1,
          margin: const EdgeInsets.symmetric(vertical: 16),
          color: dividerColor,
        ),
        // Right side - Customization Options
        Expanded(
          flex: 4,
          child: Column(
            children: [
              Expanded(
                child: CustomScrollView(
                  slivers: [
                    SliverToBoxAdapter(child: _buildCookingOptions()),
                    SliverToBoxAdapter(child: const SizedBox(height: 24)),
                    SliverToBoxAdapter(child: _buildAddonsSection()),
                    SliverToBoxAdapter(child: const SizedBox(height: 24)),
                    SliverToBoxAdapter(child: _buildExtrasSection()),
                    SliverToBoxAdapter(child: const SizedBox(height: 24)),
                    SliverToBoxAdapter(child: _buildAllergyOptions()),
                    SliverToBoxAdapter(child: const SizedBox(height: 24)),
                    SliverToBoxAdapter(child: _buildNotesSection()),
                  ],
                ),
              ),
              _buildAddToCartButton(totalPrice),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPortraitLayout(double totalPrice) {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(child: _buildHeader()),
        SliverToBoxAdapter(child: const SizedBox(height: 16)),
        SliverToBoxAdapter(child: _buildDescription()),
        SliverToBoxAdapter(child: const SizedBox(height: 24)),
        SliverToBoxAdapter(child: _buildCookingOptions()),
        SliverToBoxAdapter(child: const SizedBox(height: 24)),
        SliverToBoxAdapter(child: _buildAddonsSection()),
        SliverToBoxAdapter(child: const SizedBox(height: 24)),
        SliverToBoxAdapter(child: _buildExtrasSection()),
        SliverToBoxAdapter(child: const SizedBox(height: 24)),
        SliverToBoxAdapter(child: _buildAllergyOptions()),
        SliverToBoxAdapter(child: const SizedBox(height: 24)),
        SliverToBoxAdapter(child: _buildNotesSection()),
        SliverToBoxAdapter(child: const SizedBox(height: 100)),
        SliverToBoxAdapter(child: _buildAddToCartButton(totalPrice)),
      ],
    );
  }

  Widget _buildDragHandle() {
    return Container(
      width: 60,
      height: 6,
      margin: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade600,
        borderRadius: BorderRadius.circular(3),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 200,
      width: double.infinity,
      child: Stack(
        children: [
          // Food image with gradient overlay
          ClipRRect(
            borderRadius: BorderRadius.circular(0),
            child: Container(
              width: double.infinity,
              height: 200,
              child: ShaderMask(
                shaderCallback: (Rect bounds) {
                  return LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.transparent, Colors.black.withOpacity(0.9)],
                  ).createShader(bounds);
                },
                blendMode: BlendMode.darken,
                child: Image.network(
                  widget.item.image,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),

          // Item name and price overlay
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.item.name,
                  style: GoogleFonts.poppins(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  '\$${widget.item.price.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 22,
                    fontWeight: FontWeight.w600,
                    color: accentColor,
                  ),
                ),
              ],
            ),
          ),

          // Close button
          Positioned(
            top: 12,
            right: 12,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Description',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.item.description,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: secondaryTextColor,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.access_time, size: 16, color: secondaryTextColor),
              const SizedBox(width: 4),
              Text(
                '${widget.item.prepTime} min',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: secondaryTextColor,
                ),
              ),
              const SizedBox(width: 12),
              Icon(Icons.local_fire_department, size: 16, color: Colors.orange),
              const SizedBox(width: 4),
              Text(
                '320 cal',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: secondaryTextColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCookingOptions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Cooking Preference',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: cookingOptions.map((option) {
              final isSelected = selectedCookingOption == option;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    selectedCookingOption = option;
                  });
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? primaryColor.withOpacity(0.2)
                        : surfaceColor,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected ? primaryColor : dividerColor,
                      width: 1.5,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isSelected)
                        Icon(
                          Icons.check_circle,
                          size: 18,
                          color: primaryColor,
                        ),
                      if (isSelected) const SizedBox(width: 6),
                      Text(
                        option,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.normal,
                          color: isSelected ? primaryColor : textColor,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAddonsSection() {
    if (dishAddons == null || dishAddons!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Add-ons',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            height: 140,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: dishAddons!.length,
              itemBuilder: (context, index) {
                final addon = dishAddons![index];
                final isSelected = selectedAddons
                    .any((selected) => selected.addonId == addon.addonId);

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        selectedAddons.removeWhere(
                            (selected) => selected.addonId == addon.addonId);
                      } else {
                        selectedAddons.add(addon);
                      }
                    });
                  },
                  child: Container(
                    width: 120,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color: cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: isSelected ? primaryColor : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Stack(
                          children: [
                            ClipRRect(
                              borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(14)),
                              child: Image.network(
                                addon.imgUrl,
                                height: 80,
                                width: double.infinity,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    height: 80,
                                    color: surfaceColor,
                                    child: Icon(Icons.fastfood,
                                        color: secondaryTextColor),
                                  );
                                },
                              ),
                            ),
                            if (isSelected)
                              Positioned(
                                top: 5,
                                right: 5,
                                child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: primaryColor,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 12,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                addon.name,
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: textColor,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                '\$${addon.priceAsDouble.toStringAsFixed(2)}',
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: accentColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExtrasSection() {
    if (dishExtras == null || dishExtras!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Extras',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            height: 140,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: dishExtras!.length,
              itemBuilder: (context, index) {
                final extra = dishExtras![index];
                final isSelected = selectedExtras
                    .any((selected) => selected.extraId == extra.extraId);

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      if (isSelected) {
                        selectedExtras.removeWhere(
                            (selected) => selected.extraId == extra.extraId);
                      } else {
                        selectedExtras.add(extra);
                      }
                    });
                  },
                  child: Container(
                    width: 120,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color: cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: isSelected ? primaryColor : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Stack(
                          children: [
                            ClipRRect(
                              borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(14)),
                              child: Image.network(
                                extra.imgUrl,
                                height: 80,
                                width: double.infinity,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    height: 80,
                                    color: surfaceColor,
                                    child: Icon(Icons.fastfood,
                                        color: secondaryTextColor),
                                  );
                                },
                              ),
                            ),
                            if (isSelected)
                              Positioned(
                                top: 5,
                                right: 5,
                                child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: primaryColor,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 12,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                extra.name,
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: textColor,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                CurrencyFormatter.format(extra.priceAsDouble),
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: accentColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllergyOptions() {
    if (allergies == null || allergies!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Allergy Information',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: allergies!.map((allergy) {
              final isSelected = selectedAllergies
                  .any((selected) => selected.allergyId == allergy.allergyId);
              return GestureDetector(
                onTap: () {
                  setState(() {
                    if (isSelected) {
                      selectedAllergies.removeWhere((selected) =>
                          selected.allergyId == allergy.allergyId);
                    } else {
                      selectedAllergies.add(allergy);
                    }
                  });
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? primaryColor.withOpacity(0.2)
                        : surfaceColor,
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(
                      color: isSelected ? primaryColor : dividerColor,
                      width: 1.5,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isSelected)
                        Icon(
                          Icons.check,
                          size: 16,
                          color: primaryColor,
                        ),
                      if (isSelected) const SizedBox(width: 4),
                      Text(
                        allergy.name,
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.normal,
                          color: isSelected ? primaryColor : textColor,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Special Instructions',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: notesController,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: textColor,
            ),
            decoration: InputDecoration(
              hintText: 'Add any special instructions...',
              hintStyle: GoogleFonts.poppins(
                fontSize: 14,
                color: secondaryTextColor.withOpacity(0.7),
              ),
              filled: true,
              fillColor: surfaceColor,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: dividerColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: primaryColor, width: 1.5),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildAddToCartButton(double totalPrice) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: surfaceColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Total Price',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: secondaryTextColor,
                    ),
                  ),
                  Text(
                    '\$${totalPrice.toStringAsFixed(2)}',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: () async {
                  // Convert selected addons to backend format
                  final List<cart_models.DishAddon> cartAddons = selectedAddons
                      .map((addon) => cart_models.DishAddon(
                            id: addon.addonId,
                            quantity: 1, // Default quantity
                          ))
                      .toList();

                  // Convert selected extras to backend format
                  final List<cart_models.DishExtra> cartExtras = selectedExtras
                      .map((extra) => cart_models.DishExtra(
                            id: extra.extraId,
                            quantity: 1, // Default quantity
                          ))
                      .toList();

                  // Convert selected allergies to backend format (just IDs)
                  final List<String> allergyIds = selectedAllergies
                      .map((allergy) => allergy.allergyId)
                      .toList();

                  // Create a detailed order object for local display
                  final orderDetails = {
                    'type': 'customized',
                    'baseItemId': widget.item.id,
                    'cookingOption': selectedCookingOption,
                    'allergies': selectedAllergies.map((a) => a.name).toList(),
                    'addons': selectedAddons.map((a) => a.name).toList(),
                    'extras': selectedExtras.map((e) => e.name).toList(),
                    'notes': notesController.text.trim(),
                    'addedOn': DateTime.now().toIso8601String(),
                    // Backend format data
                    'backendData': {
                      'allergyIds': allergyIds,
                      'dishAddons': cartAddons.map((a) => a.toJson()).toList(),
                      'dishExtras': cartExtras.map((e) => e.toJson()).toList(),
                      'notes': notesController.text.trim(),
                    },
                  };

                  // Check if we have customization data
                  final hasCustomization = selectedAddons.isNotEmpty ||
                      selectedExtras.isNotEmpty ||
                      selectedAllergies.isNotEmpty ||
                      notesController.text.trim().isNotEmpty;

                  if (hasCustomization) {
                    // For customized items: Update existing cart item with customization
                    debugPrint(
                        '✅Adding customized item to cart with details: $orderDetails');
                    await _addCustomizedItemToCart(orderDetails);
                  } else {
                    // For non-customized items: Use standard add (only if no cart item ID provided)
                    if (widget.cartItemId == null) {
                      context.read<POSBloc>().add(AddToCart(
                            widget.item,
                            id: widget.item.id,
                            name: widget.item.name,
                            price: totalPrice,
                            customization: orderDetails,
                          ));
                    }
                    // If cartItemId is provided but no customization, just close modal
                  }

                  if (mounted) {
                    Navigator.of(context).pop();
                  }
                },
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  backgroundColor: primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  elevation: 0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.shopping_cart_outlined),
                    const SizedBox(width: 8),
                    Text(
                      'Add to Cart',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
