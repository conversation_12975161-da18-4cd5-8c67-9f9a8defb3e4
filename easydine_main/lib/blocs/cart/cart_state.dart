import 'package:equatable/equatable.dart';
import '../../models/cart_models.dart';

enum CartStatus {
  initial,
  loading,
  loaded,
  error,
  processing, // For operations like add/update/delete items
}

class CartState extends Equatable {
  final CartStatus status;
  final Cart? currentCart;
  final List<Cart> allCarts;
  final String? error;
  final bool isProcessing;
  final String?
      originalOrderId; // Track original order ID when loaded from running orders

  const CartState({
    this.status = CartStatus.initial,
    this.currentCart,
    this.allCarts = const [],
    this.error,
    this.isProcessing = false,
    this.originalOrderId,
  });

  /// Get total amount from current cart
  double get total {
    if (currentCart?.total != null) {
      return double.tryParse(currentCart!.total) ?? 0.0;
    }
    return 0.0;
  }

  /// Get number of items in current cart
  int get itemCount => currentCart?.items.length ?? 0;

  /// Check if cart is empty
  bool get isEmpty => itemCount == 0;

  /// Check if cart has items
  bool get hasItems => itemCount > 0;

  /// Get cart status string
  String get cartStatus => currentCart?.status ?? 'UNKNOWN';

  /// Check if cart is active
  bool get isActive => cartStatus == 'ACTIVE';

  /// Check if cart is on hold
  bool get isOnHold => cartStatus == 'HOLD';

  /// Check if cart is loaded and ready
  bool get isReady => status == CartStatus.loaded && currentCart != null;

  /// Check if there's an error
  bool get hasError => status == CartStatus.error && error != null;

  /// Check if cart is loading
  bool get isLoading => status == CartStatus.loading;

  CartState copyWith({
    CartStatus? status,
    Cart? currentCart,
    List<Cart>? allCarts,
    String? error,
    bool? isProcessing,
    String? originalOrderId,
    bool clearError = false,
    bool clearCurrentCart = false,
    bool clearOriginalOrderId = false,
  }) {
    return CartState(
      status: status ?? this.status,
      currentCart: clearCurrentCart ? null : (currentCart ?? this.currentCart),
      allCarts: allCarts ?? this.allCarts,
      error: clearError ? null : (error ?? this.error),
      isProcessing: isProcessing ?? this.isProcessing,
      originalOrderId: clearOriginalOrderId
          ? null
          : (originalOrderId ?? this.originalOrderId),
    );
  }

  @override
  List<Object?> get props => [
        status,
        currentCart,
        allCarts,
        error,
        isProcessing,
        originalOrderId,
      ];
}

/// Initial state
class CartInitial extends CartState {
  const CartInitial() : super(status: CartStatus.initial);
}

/// Loading state
class CartLoading extends CartState {
  const CartLoading() : super(status: CartStatus.loading);
}

/// Loaded state with cart data
class CartLoaded extends CartState {
  const CartLoaded({
    required Cart cart,
    List<Cart> allCarts = const [],
    String? originalOrderId,
  }) : super(
          status: CartStatus.loaded,
          currentCart: cart,
          allCarts: allCarts,
          originalOrderId: originalOrderId,
        );
}

/// Error state
class CartError extends CartState {
  const CartError({required String error})
      : super(
          status: CartStatus.error,
          error: error,
        );
}

/// Processing state (for operations)
class CartProcessing extends CartState {
  const CartProcessing({
    Cart? currentCart,
    List<Cart> allCarts = const [],
  }) : super(
          status: CartStatus.processing,
          currentCart: currentCart,
          allCarts: allCarts,
          isProcessing: true,
        );
}
