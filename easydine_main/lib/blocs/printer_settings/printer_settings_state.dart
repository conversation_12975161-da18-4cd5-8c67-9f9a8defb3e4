// lib/blocs/printer_settings/printer_settings_state.dart

part of 'printer_settings_bloc.dart';

class PrinterSettingsState {
  final PrinterSettings settings;
  final List<String> availableCategories;
  final List<String> customLocations;
  final bool isLoading;
  final bool isDiscovering;
  final String? error;

  PrinterSettingsState({
    required this.settings,
    required this.availableCategories,
    required this.customLocations,
    required this.isLoading,
    required this.isDiscovering,
    this.error,
  });

  factory PrinterSettingsState.initial() {
    return PrinterSettingsState(
      settings: PrinterSettings.empty(),
      availableCategories: [],
      customLocations: [],
      isLoading: false,
      isDiscovering: false,
    );
  }

  PrinterSettingsState copyWith({
    PrinterSettings? settings,
    List<String>? availableCategories,
    List<String>? customLocations,
    bool? isLoading,
    bool? isDiscovering,
    String? error,
  }) {
    return PrinterSettingsState(
      settings: settings ?? this.settings,
      availableCategories: availableCategories ?? this.availableCategories,
      customLocations: customLocations ?? this.customLocations,
      isLoading: isLoading ?? this.isLoading,
      isDiscovering: isDiscovering ?? this.isDiscovering,
      error: error,
    );
  }
}
