import 'package:equatable/equatable.dart';
import '../../models/table_model.dart';
import '../../models/floor_model.dart';
import '../../models/order_model.dart';
import '../../models/bill_model.dart';

class TableState extends Equatable {
  final List<TableModel> tables;
  final List<FloorModel> floors;
  final bool isLoading;
  final String? error;
  final String? filterStatus;
  final int? filterSeats;
  final List<String> availableFloorIds;
  final String? currentFloorId;
  final OrderModel? currentOrder;
  final TableBillResponse? currentTableBill;
  final bool isFetchingBill;

  const TableState({
    this.tables = const [],
    this.floors = const [],
    this.isLoading = false,
    this.error,
    this.filterStatus,
    this.filterSeats,
    this.availableFloorIds = const [],
    this.currentFloorId,
    this.currentOrder,
    this.currentTableBill,
    this.isFetchingBill = false,
  });

  TableState copyWith({
    List<TableModel>? tables,
    List<FloorModel>? floors,
    bool? isLoading,
    String? error,
    String? filterStatus,
    int? filterSeats,
    List<String>? availableFloorIds,
    String? currentFloorId,
    OrderModel? currentOrder,
    TableBillResponse? currentTableBill,
    bool? isFetchingBill,
  }) {
    return TableState(
      tables: tables ?? this.tables,
      floors: floors ?? this.floors,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      filterStatus: filterStatus ?? this.filterStatus,
      filterSeats: filterSeats ?? this.filterSeats,
      availableFloorIds: availableFloorIds ?? this.availableFloorIds,
      currentFloorId: currentFloorId ?? this.currentFloorId,
      currentOrder: currentOrder ?? this.currentOrder,
      currentTableBill: currentTableBill ?? this.currentTableBill,
      isFetchingBill: isFetchingBill ?? this.isFetchingBill,
    );
  }

  @override
  List<Object?> get props => [
        tables,
        floors,
        isLoading,
        error,
        filterStatus,
        filterSeats,
        availableFloorIds,
        currentFloorId,
        currentOrder,
        currentTableBill,
        isFetchingBill,
      ];
}
