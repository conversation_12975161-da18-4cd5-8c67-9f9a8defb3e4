import 'package:equatable/equatable.dart';
import 'dart:ui';

abstract class TableEvent extends Equatable {
  const TableEvent();

  @override
  List<Object?> get props => [];
}

class LoadTables extends TableEvent {}

class LoadFloorsAndTables extends TableEvent {}

class UpdateTableStatus extends TableEvent {
  final String tableId;
  final String newStatus;

  const UpdateTableStatus({
    required this.tableId,
    required this.newStatus,
  });

  @override
  List<Object> get props => [tableId, newStatus];
}

class UpdateTableCleaningStatus extends TableEvent {
  final String tableId;
  final String newStatus;

  const UpdateTableCleaningStatus({
    required this.tableId,
    required this.newStatus,
  });

  @override
  List<Object> get props => [tableId, newStatus];
}

class UpdateTableBookedSeats extends TableEvent {
  final String tableId;
  final int bookedSeats;

  const UpdateTableBookedSeats({
    required this.tableId,
    required this.bookedSeats,
  });

  @override
  List<Object> get props => [tableId, bookedSeats];
}

class ReserveTable extends TableEvent {
  final String tableId;
  final int bookedSeats;
  final Map<String, dynamic>? reservationDetails;

  const ReserveTable({
    required this.tableId,
    required this.bookedSeats,
    this.reservationDetails,
  });

  @override
  List<Object?> get props => [tableId, bookedSeats, reservationDetails];
}

class CancelReservation extends TableEvent {
  final String tableId;

  const CancelReservation({required this.tableId});

  @override
  List<Object> get props => [tableId];
}

class MarkTableAsOccupied extends TableEvent {
  final String tableId;
  final int bookedSeats;
  final Map<String, dynamic>? customerDetails;

  const MarkTableAsOccupied({
    required this.tableId,
    required this.bookedSeats,
    this.customerDetails,
  });

  @override
  List<Object?> get props => [tableId, bookedSeats, customerDetails];
}

class MarkTableAsAvailable extends TableEvent {
  final String tableId;

  const MarkTableAsAvailable({required this.tableId});

  @override
  List<Object> get props => [tableId];
}

class ResetTable extends TableEvent {
  final String tableId;

  const ResetTable({required this.tableId});

  @override
  List<Object> get props => [tableId];
}

class UpdateTableLayout extends TableEvent {
  final String tableId;
  final Offset position;
  final Size size;

  const UpdateTableLayout({
    required this.tableId,
    required this.position,
    required this.size,
  });

  @override
  List<Object> get props => [tableId, position, size];
}

class UpdateTableFilters extends TableEvent {
  final String? filterStatus;
  final int? filterSeats;

  const UpdateTableFilters({
    this.filterStatus,
    this.filterSeats,
  });

  @override
  List<Object?> get props => [filterStatus, filterSeats];
}

class UpdateCurrentFloor extends TableEvent {
  final int floor;

  const UpdateCurrentFloor(this.floor);

  @override
  List<Object> get props => [floor];
}

class ViewTableBill extends TableEvent {
  final String tableId;

  const ViewTableBill({required this.tableId});

  @override
  List<Object> get props => [tableId];
}

class FetchTableBill extends TableEvent {
  final String tableId;

  const FetchTableBill({required this.tableId});

  @override
  List<Object> get props => [tableId];
}
