import 'package:flutter_test/flutter_test.dart';
import 'package:easydine_main/utils/currency_formatter.dart';
import 'package:easydine_main/services/currency_service.dart';
import 'package:easydine_main/models/currency_settings.dart';

void main() {
  group('CurrencyFormatter Tests', () {
    setUp(() async {
      // Clear cache before each test
      CurrencyFormatter.clearCache();
    });

    test('should format price with default currency (USD)', () {
      final formatted = CurrencyFormatter.format(123.45);
      expect(formatted, '\$123.45');
    });

    test('should format price with thousands separator', () {
      final formatted = CurrencyFormatter.format(1234.56);
      expect(formatted, '\$1,234.56');
    });

    test('should format price with millions', () {
      final formatted = CurrencyFormatter.format(1234567.89);
      expect(formatted, '\$1,234,567.89');
    });

    test('should get correct currency symbol', () {
      final symbol = CurrencyFormatter.getSymbol();
      expect(symbol, '\$'); // Default USD symbol
    });

    test('should get correct currency code', () {
      final code = CurrencyFormatter.getCode();
      expect(code, 'USD'); // Default USD code
    });

    test('should format compact numbers correctly', () {
      expect(CurrencyFormatter.formatCompact(500), '\$500.00');
      expect(CurrencyFormatter.formatCompact(1500), '\$1.50K');
      expect(CurrencyFormatter.formatCompact(1500000), '\$1.50M');
    });

    test('should format number only without currency symbol', () {
      final formatted = CurrencyFormatter.formatNumberOnly(1234.56);
      expect(formatted, '1,234.56');
    });

    test('should parse formatted price back to double', () {
      final price = CurrencyFormatter.parsePrice('\$1,234.56');
      expect(price, 1234.56);
    });

    test('should handle different currency settings', () {
      // Test with EUR settings
      const eurSettings = CurrencySettings(
        currencyCode: 'EUR',
        currencySymbol: '€',
        currencyName: 'Euro',
        symbolBefore: true,
      );
      
      final formatted = eurSettings.formatPrice(123.45);
      expect(formatted, '€123.45');
    });

    test('should handle currency symbol placement', () {
      // Test symbol after amount
      const jpySettings = CurrencySettings(
        currencyCode: 'JPY',
        currencySymbol: '¥',
        currencyName: 'Japanese Yen',
        symbolBefore: false,
        decimalPlaces: 0,
      );
      
      final formatted = jpySettings.formatPrice(123);
      expect(formatted, '123¥');
    });

    test('should handle different decimal places', () {
      const jpySettings = CurrencySettings(
        currencyCode: 'JPY',
        currencySymbol: '¥',
        currencyName: 'Japanese Yen',
        symbolBefore: true,
        decimalPlaces: 0,
      );
      
      final formatted = jpySettings.formatPrice(123.99);
      expect(formatted, '¥124'); // Should round to 0 decimal places
    });

    test('should handle custom separators', () {
      const customSettings = CurrencySettings(
        currencyCode: 'EUR',
        currencySymbol: '€',
        currencyName: 'Euro',
        symbolBefore: true,
        decimalSeparator: ',',
        thousandsSeparator: '.',
      );
      
      final formatted = customSettings.formatPrice(1234.56);
      expect(formatted, '€1.234,56');
    });
  });

  group('Currency Settings Integration Tests', () {
    test('should use available currency settings', () {
      final currencies = CurrencySettings.availableCurrencies;
      expect(currencies.length, greaterThan(0));
      
      // Check that USD is available
      final usd = currencies.firstWhere((c) => c.currencyCode == 'USD');
      expect(usd.currencySymbol, '\$');
      expect(usd.currencyName, 'US Dollar');
    });

    test('should have INR currency available', () {
      final currencies = CurrencySettings.availableCurrencies;
      final inr = currencies.firstWhere((c) => c.currencyCode == 'INR');
      expect(inr.currencySymbol, '₹');
      expect(inr.currencyName, 'Indian Rupee');
    });

    test('should have GBP currency available', () {
      final currencies = CurrencySettings.availableCurrencies;
      final gbp = currencies.firstWhere((c) => c.currencyCode == 'GBP');
      expect(gbp.currencySymbol, '£');
      expect(gbp.currencyName, 'British Pound');
    });

    test('should have EUR currency available', () {
      final currencies = CurrencySettings.availableCurrencies;
      final eur = currencies.firstWhere((c) => c.currencyCode == 'EUR');
      expect(eur.currencySymbol, '€');
      expect(eur.currencyName, 'Euro');
    });
  });
}
